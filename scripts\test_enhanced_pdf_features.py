#!/usr/bin/env python3
"""
Test script for enhanced PDF features including:
- Advanced layout analysis
- Intelligent content extraction  
- Enhanced column detection
- Content-aware chunking

This script demonstrates the new high-priority enhancements for the PDF RAG system.
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.advanced_layout_analyzer import AdvancedLayoutAnalyzer
from app.services.intelligent_content_extractor import IntelligentContentExtractor
from app.services.enhanced_column_detector import EnhancedColumnDetector
from app.services.enhanced_chunking_service import EnhancedChunkingService
from app.services.pdf_processor import extract_text_with_rag

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_advanced_layout_analysis(pdf_path: str):
    """Test advanced layout analysis functionality"""
    logger.info("=" * 60)
    logger.info("Testing Advanced Layout Analysis")
    logger.info("=" * 60)
    
    try:
        # Initialize the layout analyzer
        layout_analyzer = AdvancedLayoutAnalyzer()
        
        # Perform layout analysis
        logger.info(f"Analyzing layout for: {pdf_path}")
        layout_analysis = layout_analyzer.analyze_document_layout(pdf_path)
        
        # Display results
        logger.info("Layout Analysis Results:")
        logger.info(f"  - Document Pattern: {layout_analysis.get('layout_pattern', {}).get('pattern', 'unknown')}")
        logger.info(f"  - Confidence: {layout_analysis.get('layout_pattern', {}).get('confidence', 0):.2f}")
        logger.info(f"  - Total Pages: {layout_analysis.get('analysis_metadata', {}).get('total_pages', 0)}")
        logger.info(f"  - Overall Confidence: {layout_analysis.get('analysis_metadata', {}).get('confidence_score', 0):.2f}")
        
        # Display recommendations
        recommendations = layout_analysis.get('recommendations', [])
        if recommendations:
            logger.info("  - Recommendations:")
            for rec in recommendations:
                logger.info(f"    * {rec}")
        
        # Display page-level analysis
        page_layouts = layout_analysis.get('page_layouts', [])
        if page_layouts:
            logger.info(f"  - Page-level Analysis (showing first 3 pages):")
            for i, page_layout in enumerate(page_layouts[:3]):
                logger.info(f"    Page {i+1}:")
                logger.info(f"      * Layout Complexity: {page_layout.get('layout_complexity', 0):.2f}")
                logger.info(f"      * Column Count: {page_layout.get('column_structure', {}).get('column_count', 1)}")
                logger.info(f"      * Content Density: {page_layout.get('content_density', {}).get('overall_density', 0):.2f}")
                logger.info(f"      * Regions: {len(page_layout.get('regions', []))}")
        
        return layout_analysis
        
    except Exception as e:
        logger.error(f"Advanced layout analysis failed: {e}")
        return None

def test_intelligent_content_extraction(pdf_path: str):
    """Test intelligent content extraction functionality"""
    logger.info("=" * 60)
    logger.info("Testing Intelligent Content Extraction")
    logger.info("=" * 60)
    
    try:
        # Initialize the content extractor
        content_extractor = IntelligentContentExtractor()
        
        # Perform content extraction
        logger.info(f"Extracting content from: {pdf_path}")
        content_analysis = content_extractor.extract_hierarchical_text(pdf_path)
        
        # Display results
        logger.info("Content Extraction Results:")
        logger.info(f"  - Total Text Blocks: {content_analysis.get('extraction_metadata', {}).get('total_blocks', 0)}")
        logger.info(f"  - Hierarchy Levels: {content_analysis.get('extraction_metadata', {}).get('hierarchy_levels', 0)}")
        logger.info(f"  - Semantic Relations: {content_analysis.get('extraction_metadata', {}).get('semantic_relations_count', 0)}")
        
        # Display content hierarchy
        content_hierarchy = content_analysis.get('content_hierarchy')
        if content_hierarchy:
            logger.info("  - Content Hierarchy:")
            if hasattr(content_hierarchy, 'title') and content_hierarchy.title:
                logger.info(f"    * Title: {content_hierarchy.title.text[:100]}...")
            if hasattr(content_hierarchy, 'authors') and content_hierarchy.authors:
                logger.info(f"    * Authors: <AUTHORS>
            if hasattr(content_hierarchy, 'abstract') and content_hierarchy.abstract:
                logger.info(f"    * Abstract: {content_hierarchy.abstract.text[:100]}...")
            if hasattr(content_hierarchy, 'sections') and content_hierarchy.sections:
                logger.info(f"    * Sections: {len(content_hierarchy.sections)} found")
        
        # Display semantic relations
        semantic_relations = content_analysis.get('semantic_relations', [])
        if semantic_relations:
            logger.info(f"  - Semantic Relations (showing first 5):")
            for i, relation in enumerate(semantic_relations[:5]):
                logger.info(f"    {i+1}. {relation.relationship_type} (confidence: {relation.confidence:.2f})")
        
        return content_analysis
        
    except Exception as e:
        logger.error(f"Intelligent content extraction failed: {e}")
        return None

def test_enhanced_column_detection(pdf_path: str):
    """Test enhanced column detection functionality"""
    logger.info("=" * 60)
    logger.info("Testing Enhanced Column Detection")
    logger.info("=" * 60)
    
    try:
        # Initialize the column detector
        column_detector = EnhancedColumnDetector()
        
        # For this test, we'll create sample text items
        # In a real scenario, these would come from the PDF extraction
        sample_text_items = [
            {'text': 'Sample text 1', 'x_position': 50, 'y_position': 100, 'width': 200, 'height': 20},
            {'text': 'Sample text 2', 'x_position': 300, 'y_position': 100, 'width': 200, 'height': 20},
            {'text': 'Sample text 3', 'x_position': 50, 'y_position': 150, 'width': 200, 'height': 20},
            {'text': 'Sample text 4', 'x_position': 300, 'y_position': 150, 'width': 200, 'height': 20},
        ]
        
        # Perform column detection
        logger.info(f"Detecting columns for sample text items")
        column_analysis = column_detector.detect_columns_advanced(sample_text_items)
        
        # Display results
        logger.info("Column Detection Results:")
        logger.info(f"  - Column Count: {column_analysis.get('column_count', 0)}")
        logger.info(f"  - Is Multi-Column: {column_analysis.get('is_multi_column', False)}")
        logger.info(f"  - Detection Confidence: {column_analysis.get('detection_metadata', {}).get('confidence_score', 0):.2f}")
        
        # Display column details
        columns = column_analysis.get('columns', [])
        if columns:
            logger.info("  - Column Details:")
            for i, column in enumerate(columns):
                logger.info(f"    Column {i+1}:")
                logger.info(f"      * Center X: {column.center_x:.1f}")
                logger.info(f"      * Width: {column.width:.1f}")
                logger.info(f"      * Height: {column.height:.1f}")
                logger.info(f"      * Blocks: {len(column.blocks)}")
        
        # Display cross-column relations
        cross_column_relations = column_analysis.get('cross_column_relations', [])
        if cross_column_relations:
            logger.info(f"  - Cross-Column Relations: {len(cross_column_relations)} found")
        
        return column_analysis
        
    except Exception as e:
        logger.error(f"Enhanced column detection failed: {e}")
        return None

def test_content_aware_chunking(pdf_path: str):
    """Test content-aware chunking functionality"""
    logger.info("=" * 60)
    logger.info("Testing Content-Aware Chunking")
    logger.info("=" * 60)
    
    try:
        # Initialize the chunking service
        chunking_service = EnhancedChunkingService()
        
        # Read a sample text from the PDF (first 5000 characters)
        with open(pdf_path, 'rb') as f:
            import fitz
            doc = fitz.open(stream=f.read(), filetype="pdf")
            sample_text = ""
            for page in doc[:2]:  # First 2 pages
                sample_text += page.get_text()
            doc.close()
        
        sample_text = sample_text[:5000]  # Limit to first 5000 characters
        
        # Perform content-aware chunking
        logger.info(f"Performing content-aware chunking on sample text ({len(sample_text)} chars)")
        chunks = chunking_service.content_aware_chunk(sample_text)
        
        # Display results
        logger.info("Content-Aware Chunking Results:")
        logger.info(f"  - Total Chunks: {len(chunks)}")
        
        # Display chunk details
        for i, chunk in enumerate(chunks[:3]):  # Show first 3 chunks
            logger.info(f"  Chunk {i+1}:")
            logger.info(f"    * Length: {len(chunk.page_content)} chars")
            logger.info(f"    * Type: {chunk.metadata.get('chunk_type', 'unknown')}")
            logger.info(f"    * Strategy: {chunk.metadata.get('chunk_strategy', 'unknown')}")
            logger.info(f"    * Content-Aware: {chunk.metadata.get('content_aware', False)}")
            logger.info(f"    * Preview: {chunk.page_content[:100]}...")
        
        return chunks
        
    except Exception as e:
        logger.error(f"Content-aware chunking failed: {e}")
        return None

def test_enhanced_rag_extraction(pdf_path: str):
    """Test the enhanced RAG extraction with all new features"""
    logger.info("=" * 60)
    logger.info("Testing Enhanced RAG Extraction")
    logger.info("=" * 60)
    
    try:
        # Perform enhanced RAG extraction
        logger.info(f"Performing enhanced RAG extraction on: {pdf_path}")
        extracted_pages = extract_text_with_rag(
            pdf_path=pdf_path,
            category="test",
            save_text=False,
            use_context7=False,
            column_detection=True,
            sentence_orchestration=True,
            table_strategy='advanced',
            extract_words=True,
            ignore_graphics=False,
            ignore_images=False,
            debug=True,
            use_advanced_layout_analysis=True,
            use_intelligent_content_extraction=True,
            use_enhanced_column_detection=True
        )
        
        # Display results
        logger.info("Enhanced RAG Extraction Results:")
        logger.info(f"  - Total Pages: {len(extracted_pages)}")
        
        # Display page details
        for page in extracted_pages[:2]:  # Show first 2 pages
            logger.info(f"  Page {page['page']}:")
            logger.info(f"    * Text Length: {page['metadata'].get('text_length', 0)} chars")
            logger.info(f"    * Word Count: {page['metadata'].get('word_count', 0)} words")
            logger.info(f"    * Column Count: {page['metadata'].get('column_count', 1)}")
            logger.info(f"    * Has Tables: {page['metadata'].get('has_tables', False)}")
            logger.info(f"    * Has Images: {page['metadata'].get('has_images', False)}")
            
            # Display advanced analysis if available
            if 'layout_analysis' in page['metadata']:
                layout = page['metadata']['layout_analysis']
                logger.info(f"    * Layout Complexity: {layout.get('layout_complexity', 0):.2f}")
                logger.info(f"    * Content Density: {layout.get('content_density', {}).get('overall_density', 0):.2f}")
            
            if 'content_hierarchy' in page['metadata']:
                hierarchy = page['metadata']['content_hierarchy']
                if hasattr(hierarchy, 'sections') and hierarchy.sections:
                    logger.info(f"    * Content Hierarchy: {len(hierarchy.sections)} sections")
                elif isinstance(hierarchy, dict) and 'sections' in hierarchy:
                    logger.info(f"    * Content Hierarchy: {len(hierarchy['sections'])} sections")
                else:
                    logger.info(f"    * Content Hierarchy: Available")
            
            if 'column_analysis' in page['metadata']:
                column_analysis = page['metadata']['column_analysis']
                logger.info(f"    * Enhanced Columns: {column_analysis.get('column_count', 1)} columns")
        
        return extracted_pages
        
    except Exception as e:
        logger.error(f"Enhanced RAG extraction failed: {e}")
        return None

def main():
    """Main function to run all tests"""
    parser = argparse.ArgumentParser(description="Test enhanced PDF features")
    parser.add_argument("pdf_path", help="Path to the PDF file to test")
    parser.add_argument("--skip-layout", action="store_true", help="Skip layout analysis test")
    parser.add_argument("--skip-content", action="store_true", help="Skip content extraction test")
    parser.add_argument("--skip-columns", action="store_true", help="Skip column detection test")
    parser.add_argument("--skip-chunking", action="store_true", help="Skip chunking test")
    parser.add_argument("--skip-rag", action="store_true", help="Skip RAG extraction test")
    
    args = parser.parse_args()
    
    # Check if PDF file exists
    if not os.path.exists(args.pdf_path):
        logger.error(f"PDF file not found: {args.pdf_path}")
        return 1
    
    logger.info(f"Starting enhanced PDF features test for: {args.pdf_path}")
    
    results = {}
    
    # Run tests based on arguments
    if not args.skip_layout:
        results['layout_analysis'] = test_advanced_layout_analysis(args.pdf_path)
    
    if not args.skip_content:
        results['content_extraction'] = test_intelligent_content_extraction(args.pdf_path)
    
    if not args.skip_columns:
        results['column_detection'] = test_enhanced_column_detection(args.pdf_path)
    
    if not args.skip_chunking:
        results['chunking'] = test_content_aware_chunking(args.pdf_path)
    
    if not args.skip_rag:
        results['rag_extraction'] = test_enhanced_rag_extraction(args.pdf_path)
    
    # Summary
    logger.info("=" * 60)
    logger.info("Test Summary")
    logger.info("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result is not None else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info("=" * 60)
    logger.info("Enhanced PDF features test completed!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
