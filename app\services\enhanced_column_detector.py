"""
Enhanced Column Detector for Advanced Column Detection

This module provides enhanced column detection capabilities for PDF documents,
including spatial clustering, reading order preservation, and cross-column relationship detection.
"""

import os
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
import fitz  # PyMuPDF
from dataclasses import dataclass
from app.utils.performance_monitor import performance_monitor
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger(__name__)

@dataclass
class Column:
    """Represents a column with positioning and content information"""
    id: int
    x_range: Tuple[float, float]
    y_range: Tuple[float, float]
    center_x: float
    width: float
    height: float
    blocks: List[Dict[str, Any]]
    reading_order: List[int]
    metadata: Dict[str, Any] = None

@dataclass
class ColumnStructure:
    """Represents the overall column structure of a page"""
    columns: List[Column]
    column_count: int
    is_multi_column: bool
    reading_order: List[int]
    cross_column_relations: List[Dict[str, Any]]
    confidence: float
    metadata: Dict[str, Any] = None

class EnhancedColumnDetector:
    """Enhanced column detection with spatial clustering and reading order preservation"""
    
    def __init__(self):
        # Column detection parameters
        self.clustering_params = {
            'eps': 50,  # DBSCAN epsilon parameter
            'min_samples': 3,  # Minimum samples for DBSCAN
            'column_separation_threshold': 100,  # Minimum separation between columns
            'reading_order_tolerance': 50  # Tolerance for reading order detection
        }
        
        # Reading order detection parameters
        self.reading_order_params = {
            'top_to_bottom_weight': 0.6,
            'left_to_right_weight': 0.4,
            'proximity_threshold': 30
        }
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def detect_columns_advanced(self, text_items: List[Dict], tolerance: float = 50, 
                              min_items_per_column: int = 3) -> Dict[str, Any]:
        """
        Advanced column detection with improved clustering and reading order preservation.
        
        Args:
            text_items: List of text items with positioning data
            tolerance: X-position tolerance for column clustering
            min_items_per_column: Minimum items required to consider a column
            
        Returns:
            Dictionary containing enhanced column information with reading order
        """
        try:
            logger.info(f"Starting advanced column detection for {len(text_items)} text items")
            
            # 1. Spatial clustering with DBSCAN
            columns = self._cluster_columns_spatial(text_items, tolerance)
            
            # 2. Reading order detection
            reading_order = self._detect_reading_order(columns)
            
            # 3. Column validation
            validated_columns = self._validate_columns(columns, min_items_per_column)
            
            # 4. Cross-column relationships
            cross_column_relations = self._detect_cross_column_relations(validated_columns)
            
            # 5. Build column structure
            column_structure = self._build_column_structure(validated_columns, reading_order, cross_column_relations)
            
            result = {
                'columns': validated_columns,
                'reading_order': reading_order,
                'cross_column_relations': cross_column_relations,
                'is_multi_column': len(validated_columns) > 1,
                'column_count': len(validated_columns),
                'column_structure': column_structure,
                'detection_metadata': {
                    'total_items': len(text_items),
                    'clustering_method': 'DBSCAN',
                    'confidence_score': self._calculate_detection_confidence(validated_columns, text_items)
                }
            }
            
            logger.info(f"Advanced column detection completed. Found {len(validated_columns)} columns")
            return result
            
        except Exception as e:
            logger.error(f"Advanced column detection failed: {str(e)}")
            return self._get_fallback_column_detection(text_items)
    
    def _cluster_columns_spatial(self, text_items: List[Dict], tolerance: float) -> List[Column]:
        """Cluster text items using spatial clustering with DBSCAN"""
        if not text_items:
            return []
        
        try:
            # Extract x-positions for clustering
            x_positions = np.array([[item['x_position']] for item in text_items])
            
            # Normalize positions for clustering
            scaler = StandardScaler()
            x_positions_scaled = scaler.fit_transform(x_positions)
            
            # Apply DBSCAN clustering
            clustering = DBSCAN(
                eps=self.clustering_params['eps'] / scaler.scale_[0],
                min_samples=self.clustering_params['min_samples']
            ).fit(x_positions_scaled)
            
            # Group items by cluster
            clusters = defaultdict(list)
            for i, label in enumerate(clustering.labels_):
                if label >= 0:  # Skip noise points
                    clusters[label].append(text_items[i])
            
            # Convert clusters to Column objects
            columns = []
            for cluster_id, cluster_items in clusters.items():
                if len(cluster_items) >= self.clustering_params['min_samples']:
                    column = self._create_column_from_cluster(cluster_id, cluster_items)
                    columns.append(column)
            
            # Sort columns by x-position (left to right)
            columns.sort(key=lambda c: c.center_x)
            
            return columns
            
        except Exception as e:
            logger.error(f"Error in spatial clustering: {str(e)}")
            return self._fallback_clustering(text_items, tolerance)
    
    def _create_column_from_cluster(self, cluster_id: int, cluster_items: List[Dict]) -> Column:
        """Create a Column object from a cluster of text items"""
        # Calculate column boundaries
        x_positions = [item['x_position'] for item in cluster_items]
        y_positions = [item['y_position'] for item in cluster_items]
        
        x_min, x_max = min(x_positions), max(x_positions)
        y_min, y_max = min(y_positions), max(y_positions)
        
        # Calculate column properties
        center_x = np.mean(x_positions)
        width = x_max - x_min
        height = y_max - y_min
        
        # Sort items by reading order (top to bottom, left to right)
        sorted_items = sorted(cluster_items, key=lambda item: (item['y_position'], item['x_position']))
        reading_order = list(range(len(sorted_items)))
        
        column = Column(
            id=cluster_id,
            x_range=(x_min, x_max),
            y_range=(y_min, y_max),
            center_x=center_x,
            width=width,
            height=height,
            blocks=sorted_items,
            reading_order=reading_order,
            metadata={
                'item_count': len(cluster_items),
                'cluster_id': cluster_id
            }
        )
        
        return column
    
    def _fallback_clustering(self, text_items: List[Dict], tolerance: float) -> List[Column]:
        """Fallback clustering method using simple distance-based clustering"""
        if not text_items:
            return []
        
        # Sort items by x-position
        sorted_items = sorted(text_items, key=lambda item: item['x_position'])
        
        clusters = []
        current_cluster = [sorted_items[0]]
        
        for item in sorted_items[1:]:
            # Check if item is close to the last item in current cluster
            last_item = current_cluster[-1]
            distance = abs(item['x_position'] - last_item['x_position'])
            
            if distance <= tolerance:
                current_cluster.append(item)
            else:
                if len(current_cluster) >= self.clustering_params['min_samples']:
                    clusters.append(current_cluster)
                current_cluster = [item]
        
        if len(current_cluster) >= self.clustering_params['min_samples']:
            clusters.append(current_cluster)
        
        # Convert clusters to Column objects
        columns = []
        for i, cluster in enumerate(clusters):
            column = self._create_column_from_cluster(i, cluster)
            columns.append(column)
        
        return columns
    
    def _detect_reading_order(self, columns: List[Column]) -> List[int]:
        """Detect reading order across columns"""
        if not columns:
            return []
        
        try:
            # Create a grid representation of columns
            column_grid = self._create_column_grid(columns)
            
            # Apply reading order algorithm
            reading_order = self._apply_reading_order_algorithm(column_grid)
            
            return reading_order
            
        except Exception as e:
            logger.error(f"Error detecting reading order: {str(e)}")
            return self._fallback_reading_order(columns)
    
    def _create_column_grid(self, columns: List[Column]) -> List[List[Column]]:
        """Create a grid representation of columns for reading order detection"""
        if not columns:
            return []
        
        # Sort columns by x-position
        sorted_columns = sorted(columns, key=lambda c: c.center_x)
        
        # Group columns by vertical position (if they overlap significantly)
        column_groups = []
        current_group = [sorted_columns[0]]
        
        for column in sorted_columns[1:]:
            # Check if column overlaps with current group
            overlaps = False
            for group_column in current_group:
                if self._columns_overlap(column, group_column):
                    overlaps = True
                    break
            
            if overlaps:
                current_group.append(column)
            else:
                column_groups.append(current_group)
                current_group = [column]
        
        if current_group:
            column_groups.append(current_group)
        
        return column_groups
    
    def _columns_overlap(self, col1: Column, col2: Column) -> bool:
        """Check if two columns overlap vertically"""
        y1_min, y1_max = col1.y_range
        y2_min, y2_max = col2.y_range
        
        # Check for vertical overlap
        overlap = max(0, min(y1_max, y2_max) - max(y1_min, y2_min))
        overlap_ratio = overlap / min(col1.height, col2.height)
        
        return overlap_ratio > 0.3  # 30% overlap threshold
    
    def _apply_reading_order_algorithm(self, column_grid: List[List[Column]]) -> List[int]:
        """Apply reading order algorithm to column grid"""
        reading_order = []
        
        for row in column_grid:
            # Sort columns in each row by x-position (left to right)
            sorted_row = sorted(row, key=lambda c: c.center_x)
            
            # Add column IDs to reading order
            for column in sorted_row:
                reading_order.append(column.id)
        
        return reading_order
    
    def _fallback_reading_order(self, columns: List[Column]) -> List[int]:
        """Fallback reading order detection"""
        # Simple left-to-right, top-to-bottom ordering
        sorted_columns = sorted(columns, key=lambda c: (c.y_range[0], c.center_x))
        return [column.id for column in sorted_columns]
    
    def _validate_columns(self, columns: List[Column], min_items_per_column: int) -> List[Column]:
        """Validate columns and filter out invalid ones"""
        validated_columns = []
        
        for column in columns:
            # Check minimum items requirement
            if len(column.blocks) < min_items_per_column:
                logger.debug(f"Column {column.id} has insufficient items ({len(column.blocks)} < {min_items_per_column})")
                continue
            
            # Check column width requirement
            if column.width < 50:  # Minimum width threshold
                logger.debug(f"Column {column.id} is too narrow ({column.width} < 50)")
                continue
            
            # Check column height requirement
            if column.height < 100:  # Minimum height threshold
                logger.debug(f"Column {column.id} is too short ({column.height} < 100)")
                continue
            
            validated_columns.append(column)
        
        return validated_columns
    
    def _detect_cross_column_relations(self, columns: List[Column]) -> List[Dict[str, Any]]:
        """Detect cross-column relationships"""
        relations = []
        
        if len(columns) < 2:
            return relations
        
        try:
            for i, col1 in enumerate(columns):
                for j, col2 in enumerate(columns[i+1:], i+1):
                    # Check for different types of relationships
                    relation = self._analyze_column_relationship(col1, col2)
                    if relation:
                        relations.append(relation)
            
        except Exception as e:
            logger.error(f"Error detecting cross-column relations: {str(e)}")
        
        return relations
    
    def _analyze_column_relationship(self, col1: Column, col2: Column) -> Optional[Dict[str, Any]]:
        """Analyze relationship between two columns"""
        try:
            # Calculate spatial relationship
            x_distance = abs(col1.center_x - col2.center_x)
            y_overlap = self._calculate_y_overlap(col1, col2)
            
            # Determine relationship type
            if y_overlap > 0.5:  # Significant vertical overlap
                if x_distance < self.clustering_params['column_separation_threshold']:
                    relation_type = "adjacent"
                    confidence = 0.8
                else:
                    relation_type = "parallel"
                    confidence = 0.6
            else:
                relation_type = "separate"
                confidence = 0.4
            
            relation = {
                'column1_id': col1.id,
                'column2_id': col2.id,
                'relation_type': relation_type,
                'confidence': confidence,
                'spatial_metrics': {
                    'x_distance': x_distance,
                    'y_overlap': y_overlap,
                    'center_distance': np.sqrt((col1.center_x - col2.center_x)**2 + 
                                             ((col1.y_range[0] + col1.y_range[1])/2 - 
                                              (col2.y_range[0] + col2.y_range[1])/2)**2)
                }
            }
            
            return relation
            
        except Exception as e:
            logger.error(f"Error analyzing column relationship: {str(e)}")
            return None
    
    def _calculate_y_overlap(self, col1: Column, col2: Column) -> float:
        """Calculate vertical overlap between two columns"""
        y1_min, y1_max = col1.y_range
        y2_min, y2_max = col2.y_range
        
        # Calculate overlap
        overlap = max(0, min(y1_max, y2_max) - max(y1_min, y2_min))
        
        # Calculate overlap ratio
        union = max(y1_max, y2_max) - min(y1_min, y2_min)
        overlap_ratio = overlap / union if union > 0 else 0
        
        return overlap_ratio
    
    def _build_column_structure(self, columns: List[Column], reading_order: List[int], 
                               cross_column_relations: List[Dict]) -> ColumnStructure:
        """Build comprehensive column structure"""
        try:
            # Calculate overall confidence
            confidence = self._calculate_structure_confidence(columns, cross_column_relations)
            
            # Create column structure
            structure = ColumnStructure(
                columns=columns,
                column_count=len(columns),
                is_multi_column=len(columns) > 1,
                reading_order=reading_order,
                cross_column_relations=cross_column_relations,
                confidence=confidence,
                metadata={
                    'detection_method': 'DBSCAN_spatial_clustering',
                    'total_relations': len(cross_column_relations),
                    'average_column_width': np.mean([c.width for c in columns]) if columns else 0,
                    'average_column_height': np.mean([c.height for c in columns]) if columns else 0
                }
            )
            
            return structure
            
        except Exception as e:
            logger.error(f"Error building column structure: {str(e)}")
            return self._get_fallback_column_structure(columns)
    
    def _calculate_structure_confidence(self, columns: List[Column], 
                                      cross_column_relations: List[Dict]) -> float:
        """Calculate confidence score for column structure"""
        if not columns:
            return 0.0
        
        confidence_factors = []
        
        # Factor 1: Column count (optimal range: 1-4 columns)
        column_count = len(columns)
        if 1 <= column_count <= 4:
            column_count_score = 1.0
        elif column_count == 0:
            column_count_score = 0.0
        else:
            column_count_score = max(0.0, 1.0 - (column_count - 4) * 0.1)
        confidence_factors.append(column_count_score)
        
        # Factor 2: Column balance (similar widths)
        if column_count > 1:
            widths = [c.width for c in columns]
            width_std = np.std(widths)
            width_mean = np.mean(widths)
            balance_score = max(0.0, 1.0 - (width_std / width_mean) if width_mean > 0 else 0.0)
            confidence_factors.append(balance_score)
        else:
            confidence_factors.append(1.0)
        
        # Factor 3: Relationship consistency
        if cross_column_relations:
            relation_confidences = [r['confidence'] for r in cross_column_relations]
            relation_score = np.mean(relation_confidences)
            confidence_factors.append(relation_score)
        else:
            confidence_factors.append(1.0)
        
        # Factor 4: Column validation (all columns meet requirements)
        validation_score = 1.0 if all(len(c.blocks) >= 3 for c in columns) else 0.8
        confidence_factors.append(validation_score)
        
        # Average confidence
        overall_confidence = np.mean(confidence_factors)
        
        return min(overall_confidence, 1.0)
    
    def _calculate_detection_confidence(self, columns: List[Column], 
                                      original_items: List[Dict]) -> float:
        """Calculate overall detection confidence"""
        if not columns or not original_items:
            return 0.0
        
        # Factor 1: Coverage (percentage of items assigned to columns)
        total_items_in_columns = sum(len(c.blocks) for c in columns)
        coverage_ratio = total_items_in_columns / len(original_items)
        
        # Factor 2: Column quality (average confidence of columns)
        column_qualities = []
        for column in columns:
            # Calculate column quality based on item distribution and spacing
            if len(column.blocks) > 1:
                x_positions = [item['x_position'] for item in column.blocks]
                x_std = np.std(x_positions)
                x_mean = np.mean(x_positions)
                quality = max(0.0, 1.0 - (x_std / x_mean) if x_mean > 0 else 0.0)
                column_qualities.append(quality)
            else:
                column_qualities.append(0.5)
        
        avg_column_quality = np.mean(column_qualities) if column_qualities else 0.0
        
        # Factor 3: Spatial consistency
        spatial_consistency = 1.0
        if len(columns) > 1:
            # Check if columns are well-separated
            column_centers = [c.center_x for c in columns]
            center_distances = [abs(column_centers[i] - column_centers[i-1]) 
                              for i in range(1, len(column_centers))]
            min_distance = min(center_distances) if center_distances else 0
            spatial_consistency = min(1.0, min_distance / 100)
        
        # Weighted average
        confidence = (coverage_ratio * 0.4 + avg_column_quality * 0.4 + spatial_consistency * 0.2)
        
        return min(confidence, 1.0)
    
    def _get_fallback_column_detection(self, text_items: List[Dict]) -> Dict[str, Any]:
        """Return fallback column detection when main detection fails"""
        return {
            'columns': [],
            'reading_order': [],
            'cross_column_relations': [],
            'is_multi_column': False,
            'column_count': 0,
            'column_structure': self._get_fallback_column_structure([]),
            'detection_metadata': {
                'total_items': len(text_items),
                'clustering_method': 'fallback',
                'confidence_score': 0.0
            }
        }
    
    def _get_fallback_column_structure(self, columns: List[Column]) -> ColumnStructure:
        """Return fallback column structure"""
        return ColumnStructure(
            columns=columns,
            column_count=len(columns),
            is_multi_column=len(columns) > 1,
            reading_order=[],
            cross_column_relations=[],
            confidence=0.0,
            metadata={'detection_method': 'fallback'}
        )
