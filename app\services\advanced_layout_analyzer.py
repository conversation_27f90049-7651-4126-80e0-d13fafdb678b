"""
Advanced Layout Analyzer for Complex PDF Documents

This module provides comprehensive layout analysis capabilities for PDF documents,
including document structure detection, content hierarchy analysis, and layout pattern recognition.
"""

import os
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
import fitz  # PyMuPDF
from dataclasses import dataclass
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)

@dataclass
class LayoutRegion:
    """Represents a layout region with positioning and content information"""
    x: float
    y: float
    width: float
    height: float
    content_type: str
    confidence: float
    text_content: str = ""
    metadata: Dict[str, Any] = None

@dataclass
class DocumentStructure:
    """Represents the overall document structure"""
    layout_pattern: str
    confidence: float
    regions: List[LayoutRegion]
    hierarchy: Dict[str, Any]
    recommendations: List[str]

class AdvancedLayoutAnalyzer:
    """Advanced layout analysis for complex PDF documents"""
    
    def __init__(self):
        self.layout_patterns = {
            'academic_paper': self._detect_academic_paper_layout,
            'technical_manual': self._detect_technical_manual_layout,
            'newsletter': self._detect_newsletter_layout,
            'report': self._detect_report_layout,
            'presentation': self._detect_presentation_layout,
            'form': self._detect_form_layout
        }
        
        # Layout detection thresholds
        self.thresholds = {
            'column_separation': 50,
            'header_height': 200,
            'footer_height': 200,
            'margin_width': 50,
            'min_region_size': 100
        }
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def analyze_document_layout(self, pdf_path: str) -> Dict[str, Any]:
        """
        Comprehensive layout analysis of a PDF document.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Dictionary containing comprehensive layout analysis results
        """
        try:
            logger.info(f"Starting advanced layout analysis for: {pdf_path}")
            
            # 1. Page-level analysis
            page_layouts = self._analyze_page_layouts(pdf_path)
            
            # 2. Document structure detection
            document_structure = self._detect_document_structure(page_layouts)
            
            # 3. Content hierarchy analysis
            content_hierarchy = self._analyze_content_hierarchy(page_layouts)
            
            # 4. Layout pattern recognition
            layout_pattern = self._recognize_layout_pattern(page_layouts)
            
            # 5. Generate recommendations
            recommendations = self._generate_layout_recommendations(layout_pattern, page_layouts)
            
            analysis_result = {
                'page_layouts': page_layouts,
                'document_structure': document_structure,
                'content_hierarchy': content_hierarchy,
                'layout_pattern': layout_pattern,
                'recommendations': recommendations,
                'analysis_metadata': {
                    'total_pages': len(page_layouts),
                    'analysis_timestamp': str(np.datetime64('now')),
                    'confidence_score': self._calculate_confidence_score(page_layouts)
                }
            }
            
            logger.info(f"Layout analysis completed. Pattern: {layout_pattern['pattern']}, Confidence: {layout_pattern['confidence']:.2f}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Layout analysis failed: {str(e)}")
            return self._get_fallback_analysis()
    
    def _analyze_page_layouts(self, pdf_path: str) -> List[Dict[str, Any]]:
        """Analyze layout for each page in the document"""
        page_layouts = []
        
        try:
            doc = fitz.open(pdf_path)
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_layout = self._analyze_single_page(page, page_num)
                page_layouts.append(page_layout)
                
            doc.close()
            
        except Exception as e:
            logger.error(f"Error analyzing page layouts: {str(e)}")
        
        return page_layouts
    
    def _analyze_single_page(self, page: fitz.Page, page_num: int) -> Dict[str, Any]:
        """Analyze layout for a single page"""
        try:
            # Get page dimensions
            page_rect = page.rect
            page_width = page_rect.width
            page_height = page_rect.height
            
            # Extract text blocks with positioning
            text_blocks = self._extract_text_blocks(page)
            
            # Detect layout regions
            regions = self._detect_layout_regions(text_blocks, page_width, page_height)
            
            # Analyze column structure
            column_analysis = self._analyze_column_structure(text_blocks, page_width)
            
            # Detect headers and footers
            header_footer = self._detect_headers_footers(text_blocks, page_height)
            
            # Analyze content density
            content_density = self._analyze_content_density(text_blocks, page_width, page_height)
            
            page_layout = {
                'page_number': page_num + 1,
                'dimensions': {
                    'width': page_width,
                    'height': page_height
                },
                'regions': regions,
                'column_structure': column_analysis,
                'header_footer': header_footer,
                'content_density': content_density,
                'layout_complexity': self._calculate_layout_complexity(regions, column_analysis)
            }
            
            return page_layout
            
        except Exception as e:
            logger.error(f"Error analyzing page {page_num + 1}: {str(e)}")
            return self._get_fallback_page_layout(page_num)
    
    def _extract_text_blocks(self, page: fitz.Page) -> List[Dict[str, Any]]:
        """Extract text blocks with positioning information"""
        text_blocks = []
        
        try:
            # Get text blocks using PyMuPDF's dict method
            blocks = page.get_text("dict")["blocks"]
            
            for block in blocks:
                if block["type"] == 0:  # Text block
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text_block = {
                                'text': span["text"].strip(),
                                'x': span["bbox"][0],
                                'y': span["bbox"][1],
                                'width': span["bbox"][2] - span["bbox"][0],
                                'height': span["bbox"][3] - span["bbox"][1],
                                'font_size': span["size"],
                                'font_name': span.get("font", ""),
                                'flags': span.get("flags", 0),
                                'bbox': span["bbox"]
                            }
                            if text_block['text']:
                                text_blocks.append(text_block)
                                
        except Exception as e:
            logger.error(f"Error extracting text blocks: {str(e)}")
        
        return text_blocks
    
    def _detect_layout_regions(self, text_blocks: List[Dict], page_width: float, page_height: float) -> List[LayoutRegion]:
        """Detect layout regions based on text block positioning"""
        regions = []
        
        try:
            # Group text blocks by proximity
            grouped_blocks = self._group_text_blocks_by_proximity(text_blocks)
            
            for group in grouped_blocks:
                if not group:
                    continue
                
                # Calculate region boundaries
                x_coords = [block['x'] for block in group]
                y_coords = [block['y'] for block in group]
                widths = [block['width'] for block in group]
                heights = [block['height'] for block in group]
                
                region = LayoutRegion(
                    x=min(x_coords),
                    y=min(y_coords),
                    width=max(x_coords) + max(widths) - min(x_coords),
                    height=max(y_coords) + max(heights) - min(y_coords),
                    content_type=self._classify_region_content(group),
                    confidence=self._calculate_region_confidence(group),
                    text_content=" ".join([block['text'] for block in group]),
                    metadata={'block_count': len(group)}
                )
                
                regions.append(region)
                
        except Exception as e:
            logger.error(f"Error detecting layout regions: {str(e)}")
        
        return regions
    
    def _group_text_blocks_by_proximity(self, text_blocks: List[Dict], proximity_threshold: float = 50) -> List[List[Dict]]:
        """Group text blocks that are close to each other"""
        if not text_blocks:
            return []
        
        # Sort blocks by y-coordinate, then by x-coordinate
        sorted_blocks = sorted(text_blocks, key=lambda b: (b['y'], b['x']))
        
        groups = []
        current_group = [sorted_blocks[0]]
        
        for block in sorted_blocks[1:]:
            # Check if block is close to the last block in current group
            last_block = current_group[-1]
            distance = np.sqrt((block['x'] - last_block['x'])**2 + (block['y'] - last_block['y'])**2)
            
            if distance <= proximity_threshold:
                current_group.append(block)
            else:
                groups.append(current_group)
                current_group = [block]
        
        if current_group:
            groups.append(current_group)
        
        return groups
    
    def _classify_region_content(self, text_blocks: List[Dict]) -> str:
        """Classify the content type of a region"""
        if not text_blocks:
            return "unknown"
        
        # Analyze text characteristics
        text_content = " ".join([block['text'] for block in text_blocks])
        avg_font_size = np.mean([block['font_size'] for block in text_blocks])
        text_length = len(text_content)
        
        # Classification logic
        if avg_font_size > 14 and text_length < 200:
            return "header"
        elif avg_font_size < 10 and text_length > 500:
            return "body_text"
        elif any(char.isdigit() for char in text_content) and len(text_content.split()) < 50:
            return "metadata"
        elif text_length < 100 and avg_font_size > 12:
            return "title"
        else:
            return "content"
    
    def _calculate_region_confidence(self, text_blocks: List[Dict]) -> float:
        """Calculate confidence score for a region"""
        if not text_blocks:
            return 0.0
        
        # Factors affecting confidence
        block_count = len(text_blocks)
        avg_font_size = np.mean([block['font_size'] for block in text_blocks])
        text_length = sum(len(block['text']) for block in text_blocks)
        
        # Normalize factors
        block_score = min(block_count / 10, 1.0)
        font_score = min(avg_font_size / 20, 1.0)
        length_score = min(text_length / 1000, 1.0)
        
        # Weighted average
        confidence = (block_score * 0.4 + font_score * 0.3 + length_score * 0.3)
        
        return min(confidence, 1.0)
    
    def _analyze_column_structure(self, text_blocks: List[Dict], page_width: float) -> Dict[str, Any]:
        """Analyze column structure of the page"""
        if not text_blocks:
            return {'column_count': 0, 'columns': []}
        
        try:
            # Extract x-positions
            x_positions = [block['x'] for block in text_blocks]
            
            # Cluster x-positions to identify columns
            clusters = self._cluster_x_positions(x_positions)
            
            # Analyze each cluster
            columns = []
            for i, cluster in enumerate(clusters):
                cluster_blocks = [block for block in text_blocks if block['x'] in cluster]
                
                column = {
                    'id': i,
                    'x_range': (min(cluster), max(cluster)),
                    'block_count': len(cluster_blocks),
                    'width': max(cluster) - min(cluster),
                    'center_x': np.mean(cluster)
                }
                columns.append(column)
            
            return {
                'column_count': len(columns),
                'columns': columns,
                'is_multi_column': len(columns) > 1
            }
            
        except Exception as e:
            logger.error(f"Error analyzing column structure: {str(e)}")
            return {'column_count': 1, 'columns': [], 'is_multi_column': False}
    
    def _cluster_x_positions(self, x_positions: List[float], threshold: float = 50) -> List[List[float]]:
        """Cluster x-positions to identify columns"""
        if not x_positions:
            return []
        
        # Sort positions
        sorted_positions = sorted(set(x_positions))
        
        clusters = []
        current_cluster = [sorted_positions[0]]
        
        for pos in sorted_positions[1:]:
            if pos - current_cluster[-1] <= threshold:
                current_cluster.append(pos)
            else:
                clusters.append(current_cluster)
                current_cluster = [pos]
        
        if current_cluster:
            clusters.append(current_cluster)
        
        return clusters
    
    def _detect_headers_footers(self, text_blocks: List[Dict], page_height: float) -> Dict[str, Any]:
        """Detect headers and footers on the page"""
        headers = []
        footers = []
        
        header_threshold = self.thresholds['header_height']
        footer_threshold = page_height - self.thresholds['footer_height']
        
        for block in text_blocks:
            if block['y'] < header_threshold:
                headers.append(block)
            elif block['y'] > footer_threshold:
                footers.append(block)
        
        return {
            'headers': headers,
            'footers': footers,
            'has_header': len(headers) > 0,
            'has_footer': len(footers) > 0
        }
    
    def _analyze_content_density(self, text_blocks: List[Dict], page_width: float, page_height: float) -> Dict[str, float]:
        """Analyze content density across the page"""
        if not text_blocks:
            return {'overall_density': 0.0, 'text_coverage': 0.0}
        
        # Calculate total text area
        total_text_area = sum(block['width'] * block['height'] for block in text_blocks)
        page_area = page_width * page_height
        
        # Calculate text coverage
        text_coverage = total_text_area / page_area if page_area > 0 else 0.0
        
        # Calculate density metrics
        overall_density = len(text_blocks) / (page_width * page_height / 10000)  # blocks per 10k pixels
        
        return {
            'overall_density': overall_density,
            'text_coverage': text_coverage,
            'block_count': len(text_blocks)
        }
    
    def _calculate_layout_complexity(self, regions: List[LayoutRegion], column_analysis: Dict) -> float:
        """Calculate layout complexity score"""
        complexity_factors = []
        
        # Factor 1: Number of regions
        region_factor = min(len(regions) / 10, 1.0)
        complexity_factors.append(region_factor)
        
        # Factor 2: Column complexity
        column_factor = min(column_analysis.get('column_count', 1) / 3, 1.0)
        complexity_factors.append(column_factor)
        
        # Factor 3: Region diversity
        content_types = set(region.content_type for region in regions)
        diversity_factor = len(content_types) / 5  # Normalize to 0-1
        complexity_factors.append(diversity_factor)
        
        # Average complexity
        complexity = np.mean(complexity_factors)
        
        return min(complexity, 1.0)
    
    def _detect_document_structure(self, page_layouts: List[Dict]) -> DocumentStructure:
        """Detect overall document structure"""
        try:
            # Analyze patterns across pages
            structure_patterns = self._analyze_structure_patterns(page_layouts)
            
            # Determine document type
            document_type = self._determine_document_type(structure_patterns)
            
            # Build hierarchy
            hierarchy = self._build_document_hierarchy(page_layouts)
            
            structure = DocumentStructure(
                layout_pattern=document_type,
                confidence=self._calculate_structure_confidence(structure_patterns),
                regions=self._extract_global_regions(page_layouts),
                hierarchy=hierarchy,
                recommendations=self._generate_structure_recommendations(document_type)
            )
            
            return structure
            
        except Exception as e:
            logger.error(f"Error detecting document structure: {str(e)}")
            return self._get_fallback_document_structure()
    
    def _analyze_structure_patterns(self, page_layouts: List[Dict]) -> Dict[str, Any]:
        """Analyze structural patterns across pages"""
        patterns = {
            'consistent_headers': 0,
            'consistent_footers': 0,
            'multi_column_pages': 0,
            'complex_layouts': 0,
            'text_density_variation': []
        }
        
        for layout in page_layouts:
            if layout.get('header_footer', {}).get('has_header'):
                patterns['consistent_headers'] += 1
            if layout.get('header_footer', {}).get('has_footer'):
                patterns['consistent_footers'] += 1
            if layout.get('column_structure', {}).get('is_multi_column'):
                patterns['multi_column_pages'] += 1
            if layout.get('layout_complexity', 0) > 0.5:
                patterns['complex_layouts'] += 1
            
            density = layout.get('content_density', {}).get('overall_density', 0)
            patterns['text_density_variation'].append(density)
        
        return patterns
    
    def _determine_document_type(self, structure_patterns: Dict) -> str:
        """Determine the type of document based on structural patterns"""
        total_pages = len(structure_patterns.get('text_density_variation', []))
        
        if total_pages == 0:
            return "unknown"
        
        # Calculate percentages
        header_percentage = structure_patterns['consistent_headers'] / total_pages
        footer_percentage = structure_patterns['consistent_footers'] / total_pages
        multi_column_percentage = structure_patterns['multi_column_pages'] / total_pages
        complex_percentage = structure_patterns['complex_layouts'] / total_pages
        
        # Classification logic
        if header_percentage > 0.8 and footer_percentage > 0.8:
            return "academic_paper"
        elif multi_column_percentage > 0.5:
            return "newsletter"
        elif complex_percentage > 0.7:
            return "technical_manual"
        elif header_percentage > 0.5:
            return "report"
        else:
            return "general"
    
    def _build_document_hierarchy(self, page_layouts: List[Dict]) -> Dict[str, Any]:
        """Build document hierarchy based on layout analysis"""
        hierarchy = {
            'sections': [],
            'headers': [],
            'footers': [],
            'content_areas': []
        }
        
        for i, layout in enumerate(page_layouts):
            page_num = layout.get('page_number', i + 1)
            
            # Extract headers and footers
            header_footer = layout.get('header_footer', {})
            if header_footer.get('has_header'):
                hierarchy['headers'].append({
                    'page': page_num,
                    'content': [block['text'] for block in header_footer.get('headers', [])]
                })
            
            if header_footer.get('has_footer'):
                hierarchy['footers'].append({
                    'page': page_num,
                    'content': [block['text'] for block in header_footer.get('footers', [])]
                })
            
            # Extract content areas
            regions = layout.get('regions', [])
            for region in regions:
                if region.content_type in ['body', 'main_content']:
                    hierarchy['content_areas'].append({
                        'page': page_num,
                        'region': region.content_type,
                        'content': region.text_content[:200] + "..." if len(region.text_content) > 200 else region.text_content
                    })
        
        return hierarchy

    def _calculate_structure_confidence(self, structure_patterns: Dict) -> float:
        """Calculate confidence score for document structure detection"""
        try:
            total_pages = len(structure_patterns.get('text_density_variation', []))
            if total_pages == 0:
                return 0.0
            
            # Calculate confidence based on pattern consistency
            header_consistency = structure_patterns.get('consistent_headers', 0) / total_pages
            footer_consistency = structure_patterns.get('consistent_footers', 0) / total_pages
            column_consistency = structure_patterns.get('multi_column_pages', 0) / total_pages
            
            # Weighted average
            confidence = (header_consistency * 0.3 + 
                         footer_consistency * 0.3 + 
                         column_consistency * 0.4)
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating structure confidence: {str(e)}")
            return 0.0

    def _analyze_content_hierarchy(self, page_layouts: List[Dict]) -> Dict[str, Any]:
        """Analyze content hierarchy across pages"""
        try:
            hierarchy_analysis = {
                'total_levels': 0,
                'hierarchy_patterns': [],
                'content_distribution': {},
                'semantic_relationships': []
            }
            
            # Analyze hierarchy patterns
            for layout in page_layouts:
                regions = layout.get('regions', [])
                for region in regions:
                    if region.content_type not in hierarchy_analysis['content_distribution']:
                        hierarchy_analysis['content_distribution'][region.content_type] = 0
                    hierarchy_analysis['content_distribution'][region.content_type] += 1
            
            # Count hierarchy levels
            hierarchy_analysis['total_levels'] = len(hierarchy_analysis['content_distribution'])
            
            return hierarchy_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing content hierarchy: {str(e)}")
            return {
                'total_levels': 0,
                'hierarchy_patterns': [],
                'content_distribution': {},
                'semantic_relationships': []
            }
    
    def _recognize_layout_pattern(self, page_layouts: List[Dict]) -> Dict[str, Any]:
        """Recognize the overall layout pattern of the document"""
        try:
            # Analyze patterns across all pages
            pattern_scores = {}
            
            for pattern_name, pattern_detector in self.layout_patterns.items():
                score = pattern_detector(page_layouts)
                pattern_scores[pattern_name] = score
            
            # Find the best matching pattern
            best_pattern = max(pattern_scores.items(), key=lambda x: x[1])
            
            return {
                'pattern': best_pattern[0],
                'confidence': best_pattern[1],
                'all_scores': pattern_scores
            }
            
        except Exception as e:
            logger.error(f"Error recognizing layout pattern: {str(e)}")
            return {'pattern': 'unknown', 'confidence': 0.0, 'all_scores': {}}
    
    def _detect_academic_paper_layout(self, page_layouts: List[Dict]) -> float:
        """Detect academic paper layout patterns"""
        score = 0.0
        total_pages = len(page_layouts)
        
        if total_pages == 0:
            return 0.0
        
        for layout in page_layouts:
            # Check for consistent headers/footers
            header_footer = layout.get('header_footer', {})
            if header_footer.get('has_header'):
                score += 0.2
            if header_footer.get('has_footer'):
                score += 0.2
            
            # Check for structured content
            regions = layout.get('regions', [])
            content_types = [r.content_type for r in regions]
            if 'header' in content_types and 'body_text' in content_types:
                score += 0.3
            
            # Check for moderate complexity
            complexity = layout.get('layout_complexity', 0)
            if 0.3 <= complexity <= 0.7:
                score += 0.3
        
        return min(score / total_pages, 1.0)
    
    def _detect_technical_manual_layout(self, page_layouts: List[Dict]) -> float:
        """Detect technical manual layout patterns"""
        score = 0.0
        total_pages = len(page_layouts)
        
        if total_pages == 0:
            return 0.0
        
        for layout in page_layouts:
            # Check for high complexity
            complexity = layout.get('layout_complexity', 0)
            if complexity > 0.6:
                score += 0.4
            
            # Check for multiple columns
            column_structure = layout.get('column_structure', {})
            if column_structure.get('is_multi_column'):
                score += 0.3
            
            # Check for diverse content types
            regions = layout.get('regions', [])
            content_types = set(r.content_type for r in regions)
            if len(content_types) > 3:
                score += 0.3
        
        return min(score / total_pages, 1.0)
    
    def _detect_newsletter_layout(self, page_layouts: List[Dict]) -> float:
        """Detect newsletter layout patterns"""
        score = 0.0
        total_pages = len(page_layouts)
        
        if total_pages == 0:
            return 0.0
        
        multi_column_pages = 0
        
        for layout in page_layouts:
            # Check for multi-column layout
            column_structure = layout.get('column_structure', {})
            if column_structure.get('is_multi_column'):
                multi_column_pages += 1
                score += 0.5
            
            # Check for varied content density
            content_density = layout.get('content_density', {})
            if content_density.get('text_coverage', 0) > 0.3:
                score += 0.3
        
        # Bonus for consistent multi-column layout
        if multi_column_pages / total_pages > 0.7:
            score += 0.2
        
        return min(score / total_pages, 1.0)
    
    def _detect_report_layout(self, page_layouts: List[Dict]) -> float:
        """Detect report layout patterns"""
        score = 0.0
        total_pages = len(page_layouts)
        
        if total_pages == 0:
            return 0.0
        
        for layout in page_layouts:
            # Check for headers
            header_footer = layout.get('header_footer', {})
            if header_footer.get('has_header'):
                score += 0.4
            
            # Check for structured content
            regions = layout.get('regions', [])
            if len(regions) > 2:
                score += 0.3
            
            # Check for moderate complexity
            complexity = layout.get('layout_complexity', 0)
            if 0.2 <= complexity <= 0.6:
                score += 0.3
        
        return min(score / total_pages, 1.0)
    
    def _detect_presentation_layout(self, page_layouts: List[Dict]) -> float:
        """Detect presentation layout patterns"""
        score = 0.0
        total_pages = len(page_layouts)
        
        if total_pages == 0:
            return 0.0
        
        for layout in page_layouts:
            # Check for low text density
            content_density = layout.get('content_density', {})
            if content_density.get('text_coverage', 0) < 0.2:
                score += 0.4
            
            # Check for large fonts (titles)
            regions = layout.get('regions', [])
            large_font_regions = [r for r in regions if r.content_type == 'title']
            if large_font_regions:
                score += 0.3
            
            # Check for simple layout
            complexity = layout.get('layout_complexity', 0)
            if complexity < 0.4:
                score += 0.3
        
        return min(score / total_pages, 1.0)
    
    def _detect_form_layout(self, page_layouts: List[Dict]) -> float:
        """Detect form layout patterns"""
        score = 0.0
        total_pages = len(page_layouts)
        
        if total_pages == 0:
            return 0.0
        
        for layout in page_layouts:
            # Check for structured regions
            regions = layout.get('regions', [])
            if len(regions) > 5:  # Forms typically have many small regions
                score += 0.4
            
            # Check for consistent spacing
            if len(regions) > 2:
                y_positions = [r.y for r in regions]
                y_spacing = np.std(y_positions)
                if y_spacing < 100:  # Consistent spacing
                    score += 0.3
            
            # Check for metadata regions
            metadata_regions = [r for r in regions if r.content_type == 'metadata']
            if metadata_regions:
                score += 0.3
        
        return min(score / total_pages, 1.0)
    
    def _generate_layout_recommendations(self, layout_pattern: Dict, page_layouts: List[Dict]) -> List[str]:
        """Generate recommendations based on layout analysis"""
        recommendations = []
        pattern = layout_pattern.get('pattern', 'unknown')
        confidence = layout_pattern.get('confidence', 0.0)
        
        if confidence < 0.5:
            recommendations.append("Layout pattern detection confidence is low. Consider manual review.")
        
        if pattern == 'academic_paper':
            recommendations.append("Document appears to be an academic paper. Use structured extraction for better results.")
            recommendations.append("Focus on title, authors, abstract, and section headers.")
        
        elif pattern == 'technical_manual':
            recommendations.append("Document appears to be a technical manual. Use advanced column detection.")
            recommendations.append("Preserve table structures and code blocks.")
        
        elif pattern == 'newsletter':
            recommendations.append("Document appears to be a newsletter. Use multi-column text extraction.")
            recommendations.append("Preserve article boundaries and image captions.")
        
        elif pattern == 'report':
            recommendations.append("Document appears to be a report. Use hierarchical text extraction.")
            recommendations.append("Focus on executive summary and key findings.")
        
        # General recommendations based on analysis
        total_pages = len(page_layouts)
        if total_pages > 10:
            recommendations.append("Large document detected. Consider batch processing for better performance.")
        
        multi_column_pages = sum(1 for layout in page_layouts 
                               if layout.get('column_structure', {}).get('is_multi_column'))
        if multi_column_pages > total_pages * 0.5:
            recommendations.append("Multi-column layout detected. Enable advanced column detection.")
        
        return recommendations
    
    def _calculate_confidence_score(self, page_layouts: List[Dict]) -> float:
        """Calculate overall confidence score for the analysis"""
        if not page_layouts:
            return 0.0
        
        confidence_scores = []
        
        for layout in page_layouts:
            # Factor 1: Layout complexity confidence
            complexity = layout.get('layout_complexity', 0)
            complexity_confidence = 1.0 - abs(complexity - 0.5)  # Best at medium complexity
            
            # Factor 2: Region confidence
            regions = layout.get('regions', [])
            if regions:
                region_confidence = np.mean([r.confidence for r in regions])
            else:
                region_confidence = 0.0
            
            # Factor 3: Content density confidence
            content_density = layout.get('content_density', {})
            density = content_density.get('overall_density', 0)
            density_confidence = min(density / 100, 1.0)  # Normalize
            
            # Average confidence for this page
            page_confidence = (complexity_confidence + region_confidence + density_confidence) / 3
            confidence_scores.append(page_confidence)
        
        return np.mean(confidence_scores)
    
    def _get_fallback_analysis(self) -> Dict[str, Any]:
        """Return fallback analysis when main analysis fails"""
        return {
            'page_layouts': [],
            'document_structure': self._get_fallback_document_structure(),
            'content_hierarchy': {},
            'layout_pattern': {'pattern': 'unknown', 'confidence': 0.0},
            'recommendations': ['Layout analysis failed. Using fallback processing.'],
            'analysis_metadata': {
                'total_pages': 0,
                'analysis_timestamp': str(np.datetime64('now')),
                'confidence_score': 0.0
            }
        }
    
    def _get_fallback_document_structure(self) -> DocumentStructure:
        """Return fallback document structure"""
        return DocumentStructure(
            layout_pattern="unknown",
            confidence=0.0,
            regions=[],
            hierarchy={},
            recommendations=["Using fallback document structure"]
        )
    
    def _get_fallback_page_layout(self, page_num: int) -> Dict[str, Any]:
        """Return fallback page layout"""
        return {
            'page_number': page_num + 1,
            'dimensions': {'width': 0, 'height': 0},
            'regions': [],
            'column_structure': {'column_count': 1, 'columns': [], 'is_multi_column': False},
            'header_footer': {'headers': [], 'footers': [], 'has_header': False, 'has_footer': False},
            'content_density': {'overall_density': 0.0, 'text_coverage': 0.0},
            'layout_complexity': 0.0
        }
    
    def _extract_global_regions(self, page_layouts: List[Dict]) -> List[LayoutRegion]:
        """Extract global regions across all pages"""
        global_regions = []
        
        for layout in page_layouts:
            regions = layout.get('regions', [])
            global_regions.extend(regions)
        
        return global_regions
    
    def _generate_structure_recommendations(self, document_type: str) -> List[str]:
        """Generate recommendations based on document type"""
        recommendations = []
        
        if document_type == 'academic_paper':
            recommendations.extend([
                "Use structured extraction for academic content",
                "Preserve citation and reference information",
                "Extract abstract and keywords"
            ])
        elif document_type == 'technical_manual':
            recommendations.extend([
                "Use advanced table extraction",
                "Preserve code blocks and technical diagrams",
                "Extract procedure steps and warnings"
            ])
        elif document_type == 'newsletter':
            recommendations.extend([
                "Use multi-column text extraction",
                "Preserve article boundaries",
                "Extract image captions and sidebars"
            ])
        
        return recommendations
