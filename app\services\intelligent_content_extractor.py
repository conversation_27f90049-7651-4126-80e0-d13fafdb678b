"""
Intelligent Content Extractor for Hierarchical Text Extraction

This module provides intelligent content extraction capabilities for PDF documents,
including hierarchical text extraction, structure preservation, and semantic relationship detection.
"""

import os
import logging
import re
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
import fitz  # PyMuPDF
from dataclasses import dataclass
from app.utils.performance_monitor import performance_monitor
from app.services.advanced_layout_analyzer import AdvancedLayoutAnalyzer, LayoutRegion

logger = logging.getLogger(__name__)

@dataclass
class TextBlock:
    """Represents a text block with positioning and metadata"""
    text: str
    x: float
    y: float
    width: float
    height: float
    font_size: float
    font_name: str
    content_type: str
    confidence: float
    metadata: Dict[str, Any] = None

@dataclass
class ContentHierarchy:
    """Represents the hierarchical structure of content"""
    title: Optional[TextBlock] = None
    authors: List[TextBlock] = None
    abstract: Optional[TextBlock] = None
    sections: List[Dict[str, Any]] = None
    references: List[TextBlock] = None
    metadata: Dict[str, Any] = None

@dataclass
class SemanticRelationship:
    """Represents a semantic relationship between content elements"""
    source: TextBlock
    target: TextBlock
    relationship_type: str
    confidence: float
    metadata: Dict[str, Any] = None

class IntelligentContentExtractor:
    """Intelligent content extraction with structure preservation"""
    
    def __init__(self):
        self.layout_analyzer = AdvancedLayoutAnalyzer()
        
        # Content type patterns
        self.content_patterns = {
            'title': [
                r'^[A-Z][A-Z\s\-\d]+$',  # All caps titles
                r'^[A-Z][a-z\s\-\d]+$',  # Title case
                r'^[A-Z][a-z\s\-\d]+:$',  # Title with colon
            ],
            'author': [
                r'^[A-Z][a-z]+\s+[A-Z][a-z]+$',  # First Last
                r'^[A-Z][a-z]+\s+[A-Z]\.\s+[A-Z][a-z]+$',  # First M. Last
                r'^[A-Z][a-z]+,\s+[A-Z][a-z]+$',  # Last, First
            ],
            'abstract': [
                r'^Abstract[:\s]*',
                r'^ABSTRACT[:\s]*',
                r'^Summary[:\s]*',
            ],
            'section': [
                r'^\d+\.\s+[A-Z][a-z\s]+$',  # 1. Section Title
                r'^[A-Z][A-Z\s]+$',  # ALL CAPS SECTION
                r'^[A-Z][a-z\s]+$',  # Title Case Section
            ],
            'reference': [
                r'^\[\d+\]',  # [1] Reference
                r'^\d+\.\s+[A-Z]',  # 1. Author
                r'^[A-Z][a-z]+,\s+[A-Z]\.',  # Author, A.
            ]
        }
        
        # Semantic relationship types
        self.relationship_types = {
            'citation': self._detect_citation_relationship,
            'reference': self._detect_reference_relationship,
            'section_hierarchy': self._detect_section_hierarchy,
            'figure_reference': self._detect_figure_reference,
            'table_reference': self._detect_table_reference
        }
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def extract_hierarchical_text(self, pdf_path: str) -> Dict[str, Any]:
        """
        Extract text with hierarchical structure preservation.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Dictionary containing hierarchical text extraction results
        """
        try:
            logger.info(f"Starting hierarchical text extraction for: {pdf_path}")
            
            # 1. Extract text blocks with positioning
            text_blocks = self._extract_text_blocks(pdf_path)
            
            # 2. Analyze text hierarchy
            hierarchy = self._analyze_text_hierarchy(text_blocks)
            
            # 3. Preserve structural elements
            structured_content = self._preserve_structural_elements(text_blocks)
            
            # 4. Extract semantic relationships
            semantic_relations = self._extract_semantic_relationships(text_blocks)
            
            # 5. Build content hierarchy
            content_hierarchy = self._build_content_hierarchy(text_blocks, hierarchy)
            
            extraction_result = {
                'text_blocks': text_blocks,
                'hierarchy': hierarchy,
                'structured_content': structured_content,
                'semantic_relations': semantic_relations,
                'content_hierarchy': content_hierarchy,
                'extraction_metadata': {
                    'total_blocks': len(text_blocks),
                    'hierarchy_levels': len(hierarchy.get('levels', [])),
                    'semantic_relations_count': len(semantic_relations),
                    'extraction_timestamp': str(np.datetime64('now'))
                }
            }
            
            logger.info(f"Hierarchical text extraction completed. Blocks: {len(text_blocks)}, Relations: {len(semantic_relations)}")
            return extraction_result
            
        except Exception as e:
            logger.error(f"Hierarchical text extraction failed: {str(e)}")
            return self._get_fallback_extraction()
    
    def _extract_text_blocks(self, pdf_path: str) -> List[TextBlock]:
        """Extract text blocks with positioning information"""
        text_blocks = []
        
        try:
            doc = fitz.open(pdf_path)
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_blocks = self._extract_page_text_blocks(page, page_num)
                text_blocks.extend(page_blocks)
                
            doc.close()
            
        except Exception as e:
            logger.error(f"Error extracting text blocks: {str(e)}")
        
        return text_blocks
    
    def _extract_page_text_blocks(self, page: fitz.Page, page_num: int) -> List[TextBlock]:
        """Extract text blocks from a single page"""
        page_blocks = []
        
        try:
            # Get text blocks using PyMuPDF's dict method
            blocks = page.get_text("dict")["blocks"]
            
            for block in blocks:
                if block["type"] == 0:  # Text block
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"].strip()
                            if text:
                                # Classify content type
                                content_type = self._classify_text_content(text, span)
                                
                                # Calculate confidence
                                confidence = self._calculate_text_confidence(text, span)
                                
                                text_block = TextBlock(
                                    text=text,
                                    x=span["bbox"][0],
                                    y=span["bbox"][1],
                                    width=span["bbox"][2] - span["bbox"][0],
                                    height=span["bbox"][3] - span["bbox"][1],
                                    font_size=span["size"],
                                    font_name=span.get("font", ""),
                                    content_type=content_type,
                                    confidence=confidence,
                                    metadata={
                                        'page': page_num + 1,
                                        'flags': span.get("flags", 0),
                                        'bbox': span["bbox"]
                                    }
                                )
                                page_blocks.append(text_block)
                                
        except Exception as e:
            logger.error(f"Error extracting text blocks from page {page_num + 1}: {str(e)}")
        
        return page_blocks
    
    def _classify_text_content(self, text: str, span: Dict) -> str:
        """Classify the content type of text"""
        font_size = span.get("size", 12)
        flags = span.get("flags", 0)
        
        # Check for title patterns
        for pattern in self.content_patterns['title']:
            if re.match(pattern, text):
                return "title"
        
        # Check for author patterns
        for pattern in self.content_patterns['author']:
            if re.match(pattern, text):
                return "author"
        
        # Check for abstract patterns
        for pattern in self.content_patterns['abstract']:
            if re.match(pattern, text):
                return "abstract"
        
        # Check for section patterns
        for pattern in self.content_patterns['section']:
            if re.match(pattern, text):
                return "section"
        
        # Check for reference patterns
        for pattern in self.content_patterns['reference']:
            if re.match(pattern, text):
                return "reference"
        
        # Classification based on font size and flags
        if font_size > 14 or (flags & 2**4):  # Bold or large font
            return "header"
        elif font_size < 10:
            return "body_text"
        elif len(text) < 50 and font_size > 12:
            return "title"
        else:
            return "content"
    
    def _calculate_text_confidence(self, text: str, span: Dict) -> float:
        """Calculate confidence score for text classification"""
        confidence = 0.5  # Base confidence
        
        # Factor 1: Text length
        text_length = len(text)
        if 10 <= text_length <= 200:
            confidence += 0.2
        elif text_length > 200:
            confidence += 0.1
        
        # Factor 2: Font size
        font_size = span.get("size", 12)
        if 10 <= font_size <= 16:
            confidence += 0.2
        elif font_size > 16:
            confidence += 0.1
        
        # Factor 3: Text quality (alphanumeric ratio)
        alphanumeric_ratio = sum(c.isalnum() for c in text) / len(text) if text else 0
        if alphanumeric_ratio > 0.7:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def _analyze_text_hierarchy(self, text_blocks: List[TextBlock]) -> Dict[str, Any]:
        """Analyze the hierarchical structure of text blocks"""
        hierarchy = {
            'levels': [],
            'relationships': [],
            'structure_score': 0.0
        }
        
        try:
            # Sort blocks by position (top to bottom, left to right)
            sorted_blocks = sorted(text_blocks, key=lambda b: (b.y, b.x))
            
            # Identify hierarchy levels based on font size and position
            levels = self._identify_hierarchy_levels(sorted_blocks)
            hierarchy['levels'] = levels
            
            # Identify relationships between levels
            relationships = self._identify_hierarchy_relationships(levels)
            hierarchy['relationships'] = relationships
            
            # Calculate structure score
            structure_score = self._calculate_structure_score(levels, relationships)
            hierarchy['structure_score'] = structure_score
            
        except Exception as e:
            logger.error(f"Error analyzing text hierarchy: {str(e)}")
        
        return hierarchy
    
    def _identify_hierarchy_levels(self, text_blocks: List[TextBlock]) -> List[Dict[str, Any]]:
        """Identify hierarchy levels based on font size and position"""
        levels = []
        
        if not text_blocks:
            return levels
        
        # Group blocks by font size ranges
        font_sizes = [block.font_size for block in text_blocks]
        unique_sizes = sorted(set(font_sizes))
        
        # Create levels based on font size clusters
        for i, size in enumerate(unique_sizes):
            level_blocks = [block for block in text_blocks if abs(block.font_size - size) < 2]
            
            level = {
                'level': i,
                'font_size': size,
                'blocks': level_blocks,
                'content_types': list(set(block.content_type for block in level_blocks)),
                'confidence': np.mean([block.confidence for block in level_blocks])
            }
            levels.append(level)
        
        # Sort levels by font size (largest first)
        levels.sort(key=lambda x: x['font_size'], reverse=True)
        
        # Reassign level numbers
        for i, level in enumerate(levels):
            level['level'] = i
        
        return levels
    
    def _identify_hierarchy_relationships(self, levels: List[Dict]) -> List[Dict[str, Any]]:
        """Identify relationships between hierarchy levels"""
        relationships = []
        
        for i, level in enumerate(levels):
            if i == 0:  # Top level
                continue
            
            # Find parent level (next level up)
            parent_level = levels[i - 1]
            
            # Analyze relationships between current level and parent
            relationship = self._analyze_level_relationship(level, parent_level)
            if relationship:
                relationships.append(relationship)
        
        return relationships
    
    def _analyze_level_relationship(self, child_level: Dict, parent_level: Dict) -> Optional[Dict[str, Any]]:
        """Analyze relationship between two hierarchy levels"""
        try:
            # Check for structural relationships
            child_blocks = child_level['blocks']
            parent_blocks = parent_level['blocks']
            
            if not child_blocks or not parent_blocks:
                return None
            
            # Calculate average positions
            child_avg_y = np.mean([block.y for block in child_blocks])
            parent_avg_y = np.mean([block.y for block in parent_blocks])
            
            # Check if child level is below parent level
            if child_avg_y > parent_avg_y:
                relationship_type = "hierarchical"
                confidence = 0.8
            else:
                relationship_type = "parallel"
                confidence = 0.6
            
            return {
                'parent_level': parent_level['level'],
                'child_level': child_level['level'],
                'relationship_type': relationship_type,
                'confidence': confidence,
                'metadata': {
                    'child_avg_y': child_avg_y,
                    'parent_avg_y': parent_avg_y,
                    'y_difference': child_avg_y - parent_avg_y
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing level relationship: {str(e)}")
            return None
    
    def _calculate_structure_score(self, levels: List[Dict], relationships: List[Dict]) -> float:
        """Calculate overall structure score"""
        if not levels:
            return 0.0
        
        # Factor 1: Number of levels (more levels = better structure)
        level_score = min(len(levels) / 5, 1.0)
        
        # Factor 2: Relationship consistency
        if relationships:
            relationship_score = np.mean([r['confidence'] for r in relationships])
        else:
            relationship_score = 0.0
        
        # Factor 3: Level confidence
        level_confidence = np.mean([level['confidence'] for level in levels])
        
        # Weighted average
        structure_score = (level_score * 0.4 + relationship_score * 0.3 + level_confidence * 0.3)
        
        return min(structure_score, 1.0)
    
    def _preserve_structural_elements(self, text_blocks: List[TextBlock]) -> Dict[str, Any]:
        """Preserve structural elements in the text"""
        structural_elements = {
            'headers': [],
            'footers': [],
            'titles': [],
            'sections': [],
            'paragraphs': [],
            'lists': [],
            'tables': [],
            'figures': []
        }
        
        try:
            for block in text_blocks:
                content_type = block.content_type
                
                if content_type == 'title':
                    structural_elements['titles'].append(block)
                elif content_type == 'section':
                    structural_elements['sections'].append(block)
                elif content_type == 'header':
                    structural_elements['headers'].append(block)
                elif content_type == 'body_text':
                    structural_elements['paragraphs'].append(block)
                elif content_type == 'reference':
                    structural_elements['lists'].append(block)
            
            # Group related elements
            structural_elements['grouped_sections'] = self._group_related_sections(structural_elements['sections'])
            structural_elements['grouped_paragraphs'] = self._group_related_paragraphs(structural_elements['paragraphs'])
            
        except Exception as e:
            logger.error(f"Error preserving structural elements: {str(e)}")
        
        return structural_elements
    
    def _group_related_sections(self, sections: List[TextBlock]) -> List[List[TextBlock]]:
        """Group related sections together"""
        if not sections:
            return []
        
        # Sort sections by position
        sorted_sections = sorted(sections, key=lambda s: (s.y, s.x))
        
        groups = []
        current_group = [sorted_sections[0]]
        
        for section in sorted_sections[1:]:
            # Check if section is close to the last section in current group
            last_section = current_group[-1]
            distance = abs(section.y - last_section.y)
            
            if distance < 100:  # Within 100 pixels
                current_group.append(section)
            else:
                groups.append(current_group)
                current_group = [section]
        
        if current_group:
            groups.append(current_group)
        
        return groups
    
    def _group_related_paragraphs(self, paragraphs: List[TextBlock]) -> List[List[TextBlock]]:
        """Group related paragraphs together"""
        if not paragraphs:
            return []
        
        # Sort paragraphs by position
        sorted_paragraphs = sorted(paragraphs, key=lambda p: (p.y, p.x))
        
        groups = []
        current_group = [sorted_paragraphs[0]]
        
        for paragraph in sorted_paragraphs[1:]:
            # Check if paragraph is close to the last paragraph in current group
            last_paragraph = current_group[-1]
            distance = abs(paragraph.y - last_paragraph.y)
            
            if distance < 50:  # Within 50 pixels
                current_group.append(paragraph)
            else:
                groups.append(current_group)
                current_group = [paragraph]
        
        if current_group:
            groups.append(current_group)
        
        return groups
    
    def _extract_semantic_relationships(self, text_blocks: List[TextBlock]) -> List[SemanticRelationship]:
        """Extract semantic relationships between text blocks"""
        relationships = []
        
        try:
            for i, block1 in enumerate(text_blocks):
                for j, block2 in enumerate(text_blocks[i+1:], i+1):
                    # Check for different types of relationships
                    for rel_type, detector in self.relationship_types.items():
                        relationship = detector(block1, block2)
                        if relationship:
                            relationships.append(relationship)
            
        except Exception as e:
            logger.error(f"Error extracting semantic relationships: {str(e)}")
        
        return relationships
    
    def _detect_citation_relationship(self, block1: TextBlock, block2: TextBlock) -> Optional[SemanticRelationship]:
        """Detect citation relationships between text blocks"""
        # Look for citation patterns like [1], [2], etc.
        citation_pattern = r'\[(\d+)\]'
        
        if re.search(citation_pattern, block1.text) and block2.content_type == 'reference':
            match = re.search(citation_pattern, block1.text)
            if match:
                citation_num = match.group(1)
                # Check if block2 contains the same citation number
                if citation_num in block2.text:
                    return SemanticRelationship(
                        source=block1,
                        target=block2,
                        relationship_type='citation',
                        confidence=0.9,
                        metadata={'citation_number': citation_num}
                    )
        
        return None
    
    def _detect_reference_relationship(self, block1: TextBlock, block2: TextBlock) -> Optional[SemanticRelationship]:
        """Detect reference relationships between text blocks"""
        # Look for reference patterns
        if (block1.content_type == 'reference' and 
            block2.content_type == 'reference' and
            block1.text != block2.text):
            
            # Check if they're sequential references
            if abs(block1.y - block2.y) < 20:  # Close in position
                return SemanticRelationship(
                    source=block1,
                    target=block2,
                    relationship_type='reference',
                    confidence=0.7,
                    metadata={'relationship': 'sequential'}
                )
        
        return None
    
    def _detect_section_hierarchy(self, block1: TextBlock, block2: TextBlock) -> Optional[SemanticRelationship]:
        """Detect section hierarchy relationships"""
        if (block1.content_type == 'section' and 
            block2.content_type == 'section'):
            
            # Check if one is a subsection of the other
            if block1.font_size > block2.font_size and abs(block1.y - block2.y) < 100:
                return SemanticRelationship(
                    source=block1,
                    target=block2,
                    relationship_type='section_hierarchy',
                    confidence=0.8,
                    metadata={'relationship': 'parent_child'}
                )
        
        return None
    
    def _detect_figure_reference(self, block1: TextBlock, block2: TextBlock) -> Optional[SemanticRelationship]:
        """Detect figure reference relationships"""
        # Look for figure references like "Figure 1", "Fig. 1", etc.
        figure_pattern = r'(?:Figure|Fig\.?)\s*(\d+)'
        
        if re.search(figure_pattern, block1.text, re.IGNORECASE):
            match = re.search(figure_pattern, block1.text, re.IGNORECASE)
            if match:
                figure_num = match.group(1)
                # Check if block2 contains figure information
                if figure_num in block2.text:
                    return SemanticRelationship(
                        source=block1,
                        target=block2,
                        relationship_type='figure_reference',
                        confidence=0.8,
                        metadata={'figure_number': figure_num}
                    )
        
        return None
    
    def _detect_table_reference(self, block1: TextBlock, block2: TextBlock) -> Optional[SemanticRelationship]:
        """Detect table reference relationships"""
        # Look for table references like "Table 1", "Tab. 1", etc.
        table_pattern = r'(?:Table|Tab\.?)\s*(\d+)'
        
        if re.search(table_pattern, block1.text, re.IGNORECASE):
            match = re.search(table_pattern, block1.text, re.IGNORECASE)
            if match:
                table_num = match.group(1)
                # Check if block2 contains table information
                if table_num in block2.text:
                    return SemanticRelationship(
                        source=block1,
                        target=block2,
                        relationship_type='table_reference',
                        confidence=0.8,
                        metadata={'table_number': table_num}
                    )
        
        return None
    
    def _build_content_hierarchy(self, text_blocks: List[TextBlock], hierarchy: Dict) -> ContentHierarchy:
        """Build content hierarchy from text blocks and hierarchy analysis"""
        content_hierarchy = ContentHierarchy(
            authors=[],
            sections=[],
            references=[],
            metadata={}
        )
        
        try:
            # Extract title
            titles = [block for block in text_blocks if block.content_type == 'title']
            if titles:
                # Use the first title (usually the main title)
                content_hierarchy.title = titles[0]
            
            # Extract authors
            authors = [block for block in text_blocks if block.content_type == 'author']
            content_hierarchy.authors = authors
            
            # Extract abstract
            abstracts = [block for block in text_blocks if block.content_type == 'abstract']
            if abstracts:
                content_hierarchy.abstract = abstracts[0]
            
            # Extract sections
            sections = [block for block in text_blocks if block.content_type == 'section']
            content_hierarchy.sections = [
                {
                    'title': section.text,
                    'level': self._determine_section_level(section),
                    'position': (section.x, section.y),
                    'metadata': section.metadata
                }
                for section in sections
            ]
            
            # Extract references
            references = [block for block in text_blocks if block.content_type == 'reference']
            content_hierarchy.references = references
            
            # Add metadata
            content_hierarchy.metadata = {
                'total_blocks': len(text_blocks),
                'hierarchy_score': hierarchy.get('structure_score', 0.0),
                'content_types': list(set(block.content_type for block in text_blocks))
            }
            
        except Exception as e:
            logger.error(f"Error building content hierarchy: {str(e)}")
        
        return content_hierarchy
    
    def _determine_section_level(self, section: TextBlock) -> int:
        """Determine the level of a section based on font size and position"""
        # Simple level determination based on font size
        if section.font_size > 16:
            return 1
        elif section.font_size > 14:
            return 2
        elif section.font_size > 12:
            return 3
        else:
            return 4
    
    def _get_fallback_extraction(self) -> Dict[str, Any]:
        """Return fallback extraction when main extraction fails"""
        return {
            'text_blocks': [],
            'hierarchy': {'levels': [], 'relationships': [], 'structure_score': 0.0},
            'structured_content': {},
            'semantic_relations': [],
            'content_hierarchy': ContentHierarchy(),
            'extraction_metadata': {
                'total_blocks': 0,
                'hierarchy_levels': 0,
                'semantic_relations_count': 0,
                'extraction_timestamp': str(np.datetime64('now'))
            }
        }
