async function api(path, opts = {}) {
    // Get CSRF token from meta tag
    let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    // Add CSRF token to headers if not already present
    if (opts.method && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(opts.method.toUpperCase())) {
        opts.headers = opts.headers || {};
        if (!opts.headers['X-CSRFToken'] && csrfToken) {
            opts.headers['X-CSRFToken'] = csrfToken;
        }
    }

    const res = await fetch(path, opts);
    let json;

    try {
        const text = await res.text();
        // Check if response is JSON
        if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
            json = JSON.parse(text);
        } else {
            // If it's not JSON (e.g., HTML error page), create an error object
            console.warn('Received non-JSON response:', text.substring(0, 100) + '...');
            json = {
                error: 'Server returned an unexpected response format',
                details: text.includes('<!DOCTYPE') ? 'HTML error page received' : 'Non-JSON response'
            };
        }
    } catch (e) {
        console.error('Failed to parse response:', e);
        json = {
            error: 'Failed to parse server response',
            details: e.message
        };
    }

    // Handle CSRF token expiration
    if (res.status === 400 && json.error && json.error.includes('CSRF')) {
        console.warn('CSRF token expired, attempting to refresh...');

        // Try to refresh the CSRF token
        const refreshed = await refreshCSRFToken();
        if (refreshed) {
            // Retry the original request with the new token
            const newCsrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            if (opts.headers && newCsrfToken) {
                opts.headers['X-CSRFToken'] = newCsrfToken;
            }

            // Retry the request
            const retryRes = await fetch(path, opts);
            let retryJson;
            try {
                const retryText = await retryRes.text();
                if (retryText.trim().startsWith('{') || retryText.trim().startsWith('[')) {
                    retryJson = JSON.parse(retryText);
                } else {
                    retryJson = {
                        error: 'Server returned an unexpected response format after retry',
                        details: retryText.includes('<!DOCTYPE') ? 'HTML error page received' : 'Non-JSON response'
                    };
                }
            } catch (e) {
                retryJson = {
                    error: 'Failed to parse server response after retry',
                    details: e.message
                };
            }

            return { ok: retryRes.ok, status: retryRes.status, json: retryJson };
        }
    }

    return { ok: res.ok, status: res.status, json };
}

async function refreshCSRFToken() {
    try {
        const response = await fetch('/api/csrf-token', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.csrf_token) {
                // Update the meta tag with the new token
                const metaTag = document.querySelector('meta[name="csrf-token"]');
                if (metaTag) {
                    metaTag.setAttribute('content', data.csrf_token);
                    console.log('CSRF token refreshed successfully');
                    return true;
                }
            }
        }

        console.error('Failed to refresh CSRF token');
        return false;
    } catch (error) {
        console.error('Error refreshing CSRF token:', error);
        return false;
    }
}

function showLoading() {
    document.getElementById("loading").style.display = "block";
}

function hideLoading() {
    document.getElementById("loading").style.display = "none";
}

function showToast(message, type) {
    Toastify({
        text: message,
        duration: 3000,
        close: true,
        gravity: "top",
        position: "right",
        backgroundColor: type === "error" ? "#ff4444" : "#00C851",
    }).showToast();
}

async function fetchCategories() {
    const select = document.getElementById("categorySelect");
    select.disabled = true;
    select.innerHTML = `<option>Loading…</option>`;
    const { ok, json } = await api('/admin/categories');
    if (ok && json.categories) {
        select.innerHTML = json.categories.map(cat => `<option value="${cat}">${cat}</option>`).join('');
    } else {
        select.innerHTML = `<option value="">No categories</option>`;
    }
    select.disabled = false;
}

function appendMessage(sender, messageHtml) {
    const chatHistory = document.getElementById("chatHistory");
    const msgDiv = document.createElement("div");
    msgDiv.className = sender === 'AI' ? "ai-message" : "user-message";
    msgDiv.innerHTML = messageHtml;
    chatHistory.appendChild(msgDiv);
    chatHistory.scrollTop = chatHistory.scrollHeight;
}

async function ask() {
    const category = document.getElementById("categorySelect").value;
    const queryInput = document.getElementById("queryInput");
    const query = queryInput.value.trim();
    if (!category) {
        showToast("Please select a category.", "error");
        return;
    }
    if (!query) {
        showToast("Query cannot be empty.", "error");
        return;
    }
    appendMessage("User", query);
    queryInput.value = "";
    showLoading();
    const { ok, json } = await api(`/query/${category}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ query })
    });
    hideLoading();
    if (ok) {
        // Display cover images first (above the answer) if present
        if (json.document_thumbnails && Array.isArray(json.document_thumbnails) && json.document_thumbnails.length > 0) {
            const thumbsHtml = `<div class="document-thumbnails-container mb-2">` +
                json.document_thumbnails.join('') +
                `</div>`;
            appendMessage("AI", thumbsHtml);
        }
        
        appendMessage("AI", json.answer);
        
        if (json.sources) {
            const sourcesHtml = `<div class="sources"><strong>Sources:</strong><ul>` +
                json.sources.map(s => `<li><a href="${s.link}" target="_blank">${s.source}</a>: ${s.snippet}</li>`).join("") +
                `</ul></div>`;
            appendMessage("AI", sourcesHtml);
        }
        showToast("Query processed successfully", "success");
    } else {
        appendMessage("AI", `Error: ${json.error || "Unknown error"}`);
        showToast("Error processing query", "error");
    }
}

document.getElementById("queryInput").addEventListener("keydown", e => {
    if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        ask();
    }
});