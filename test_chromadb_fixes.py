#!/usr/bin/env python3
"""
Test script to verify ChromaDB instance conflict fixes.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_unified_vector_db():
    """Test the unified vector database service."""
    logger.info("Testing UnifiedVectorDB service...")
    
    try:
        from app.services.unified_vector_db import get_unified_vector_db, reset_unified_vector_db
        
        # Test 1: Get instance
        logger.info("Test 1: Getting UnifiedVectorDB instance...")
        db1 = get_unified_vector_db()
        logger.info("✓ Successfully got first instance")
        
        # Test 2: Get another instance (should be the same)
        logger.info("Test 2: Getting second UnifiedVectorDB instance...")
        db2 = get_unified_vector_db()
        logger.info("✓ Successfully got second instance")
        
        # Test 3: Verify singleton pattern
        if db1 is db2:
            logger.info("✓ Singleton pattern working correctly")
        else:
            logger.error("✗ Singleton pattern failed - instances are different")
            return False
        
        # Test 4: Test connection
        logger.info("Test 4: Testing ChromaDB connection...")
        if db1.test_connection():
            logger.info("✓ ChromaDB connection successful")
        else:
            logger.warning("⚠ ChromaDB connection failed (this might be expected if no data)")
        
        # Test 5: Get collection stats
        logger.info("Test 5: Getting collection stats...")
        stats = db1.get_collection_stats()
        logger.info(f"✓ Collection stats: {stats}")
        
        # Test 6: Test reset functionality
        logger.info("Test 6: Testing reset functionality...")
        reset_unified_vector_db()
        logger.info("✓ Reset successful")
        
        # Test 7: Get new instance after reset
        logger.info("Test 7: Getting new instance after reset...")
        db3 = get_unified_vector_db()
        if db3 is not db1:
            logger.info("✓ New instance created after reset")
        else:
            logger.error("✗ Reset failed - same instance returned")
            return False
        
        logger.info("🎉 All UnifiedVectorDB tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ UnifiedVectorDB test failed: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_vector_db_compatibility():
    """Test compatibility with existing vector_db.py interface."""
    logger.info("Testing vector_db.py compatibility...")
    
    try:
        from app.services.vector_db import get_vector_db
        
        # Test 1: Get vector DB for a category
        logger.info("Test 1: Getting vector DB for CANOPY category...")
        db1 = get_vector_db("CANOPY")
        logger.info("✓ Successfully got vector DB for CANOPY")
        
        # Test 2: Get vector DB for another category
        logger.info("Test 2: Getting vector DB for RISE category...")
        db2 = get_vector_db("RISE")
        logger.info("✓ Successfully got vector DB for RISE")
        
        # Test 3: Verify they're the same instance (unified database)
        if db1 is db2:
            logger.info("✓ Unified database working correctly")
        else:
            logger.error("✗ Unified database failed - different instances")
            return False
        
        logger.info("🎉 All vector_db.py compatibility tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ vector_db.py compatibility test failed: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_file_handling():
    """Test improved file handling."""
    logger.info("Testing improved file handling...")
    
    try:
        from app.utils.helpers import safe_remove_file
        
        # Test 1: Test with non-existent file
        logger.info("Test 1: Testing with non-existent file...")
        result = safe_remove_file("non_existent_file.txt")
        if result:
            logger.info("✓ Non-existent file handled correctly")
        else:
            logger.error("✗ Non-existent file handling failed")
            return False
        
        # Test 2: Test with existing file (create a temporary one)
        logger.info("Test 2: Testing with existing file...")
        test_file = "test_file_for_deletion.txt"
        try:
            with open(test_file, 'w') as f:
                f.write("Test content")
            
            result = safe_remove_file(test_file)
            if result:
                logger.info("✓ Existing file deletion successful")
            else:
                logger.error("✗ Existing file deletion failed")
                return False
                
        except Exception as e:
            logger.error(f"✗ File creation/deletion test failed: {str(e)}")
            return False
        
        logger.info("🎉 All file handling tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ File handling test failed: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all tests."""
    logger.info("🚀 Starting ChromaDB fixes verification tests...")
    
    tests = [
        ("UnifiedVectorDB Service", test_unified_vector_db),
        ("Vector DB Compatibility", test_vector_db_compatibility),
        ("File Handling", test_file_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running {test_name} test...")
        logger.info(f"{'='*50}")
        
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name} test PASSED")
        else:
            logger.error(f"❌ {test_name} test FAILED")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"Test Results: {passed}/{total} tests passed")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 All tests passed! ChromaDB fixes are working correctly.")
        return 0
    else:
        logger.error(f"❌ {total - passed} test(s) failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
