import os
import sqlite3
import logging
import json
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database paths
DB_PATH = os.getenv("DB_PATH", "./erdb_main.db")

def initialize_default_greetings(cursor):
    """Initialize default greeting templates if none exist."""
    try:
        # Check if any greeting templates exist
        cursor.execute("SELECT COUNT(*) FROM greeting_templates")
        count = cursor.fetchone()[0]

        if count == 0:
            logger.info("Initializing default greeting templates...")

            # Default greeting templates based on the original hardcoded greetings
            default_greetings = [
                # Welcome greetings
                ('welcome', 'Hello {name}!', '{}', 1, 3),
                ('welcome', 'Welcome to the ERDB Knowledge Hub, {name}!', '{}', 1, 2),
                ('welcome', 'Hi {name}, welcome!', '{}', 1, 2),

                # Response greetings (used in chat responses)
                ('response', 'Hello {name},', '{}', 1, 3),
                ('response', 'Hi {name},', '{}', 1, 3),
                ('response', 'Thank you for your question, {name}.', '{}', 1, 2),
                ('response', '{name}, here\'s what I found:', '{}', 1, 2),
                ('response', 'I\'d be happy to help with that, {name}.', '{}', 1, 2),
                ('response', 'Great question, {name}.', '{}', 1, 2),
                ('response', '{name}, based on the information available:', '{}', 1, 2),
                ('response', 'Here\'s what I can tell you, {name}:', '{}', 1, 2),
                ('response', '{name}, I\'ve analyzed the information and:', '{}', 1, 1),
                ('response', 'Thanks for asking, {name}. Here\'s what I know:', '{}', 1, 1),

                # Return user greetings (for future use)
                ('return_user', 'Welcome back, {name}!', '{}', 1, 3),
                ('return_user', 'Good to see you again, {name}!', '{}', 1, 2),
                ('return_user', 'Hello again, {name}!', '{}', 1, 2),
            ]

            # Insert default greetings
            for template_type, greeting_text, context_conditions, is_active, weight in default_greetings:
                cursor.execute('''
                    INSERT INTO greeting_templates
                    (template_type, greeting_text, context_conditions, is_active, weight)
                    VALUES (?, ?, ?, ?, ?)
                ''', (template_type, greeting_text, context_conditions, is_active, weight))

            logger.info(f"Inserted {len(default_greetings)} default greeting templates")
        else:
            logger.info(f"Found {count} existing greeting templates, skipping initialization")

    except Exception as e:
        logger.error(f"Error initializing default greetings: {str(e)}")
        raise

def migrate_location_extraction_schema(cursor):
    """Migrate location extraction schema for existing databases."""
    try:
        # Check if geocoding_cache table exists and has the required columns
        cursor.execute("PRAGMA table_info(geocoding_cache)")
        columns = cursor.fetchall()
        existing_columns = [col[1] for col in columns]

        # Required columns for address components
        required_columns = {
            'country': 'TEXT',
            'region': 'TEXT',
            'city': 'TEXT'
        }

        # Add missing columns
        for column_name, column_type in required_columns.items():
            if column_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE geocoding_cache ADD COLUMN {column_name} {column_type}")
                    logger.info(f"Added column {column_name} to geocoding_cache table")
                except sqlite3.Error as e:
                    logger.error(f"Failed to add column {column_name}: {e}")

        # Update database version to 2 if not already done
        cursor.execute("SELECT MAX(version) FROM database_version")
        result = cursor.fetchone()
        current_version = result[0] if result and result[0] is not None else 0

        if current_version < 2:
            cursor.execute("""
                INSERT INTO database_version (version, description)
                VALUES (2, 'Added address component columns to geocoding_cache table')
            """)
            logger.info("Updated database version to 2 for location extraction schema")

    except sqlite3.Error as e:
        logger.error(f"Error during location extraction schema migration: {e}")

def initialize_database():
    """
    Initialize the database with all required tables and indexes.
    This function creates the database schema if it doesn't exist and handles migrations.
    """
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON")

        # Create source_urls table first
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS source_urls (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT UNIQUE NOT NULL,
                title TEXT,
                description TEXT,
                last_scraped TIMESTAMP,
                last_updated TIMESTAMP,
                status TEXT CHECK(status IN ('active', 'archived', 'error')),
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Create pdf_documents table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS pdf_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                original_filename TEXT NOT NULL,
                category TEXT NOT NULL,
                upload_date TIMESTAMP NOT NULL,
                source_url_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                file_size INTEGER,
                page_count INTEGER,
                form_id INTEGER,
                updated_at TIMESTAMP,
                published_year INTEGER,
                published_month_start INTEGER,
                published_month_end INTEGER,
                published_month_range_str TEXT,
                pdf_title TEXT,
                pdf_author TEXT,
                pdf_subject TEXT,
                pdf_keywords TEXT,
                pdf_creation_date TIMESTAMP,
                pdf_modification_date TIMESTAMP,
                pdf_version TEXT,
                pdf_producer TEXT,
                pdf_creator TEXT,
                FOREIGN KEY (source_url_id) REFERENCES source_urls(id) ON DELETE SET NULL
            )
        ''')

        # --- AUTOMATIC COLUMN MIGRATION FOR pdf_documents ---
        cursor.execute("PRAGMA table_info(pdf_documents)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add missing columns if they don't exist
        if 'file_size' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN file_size INTEGER')
            logger.info("Added file_size column to pdf_documents")
        if 'page_count' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN page_count INTEGER')
            logger.info("Added page_count column to pdf_documents")
        if 'form_id' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN form_id INTEGER REFERENCES forms(id) ON DELETE SET NULL')
            logger.info("Added form_id column to pdf_documents")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_pdf_documents_form_id ON pdf_documents(form_id)")
        if 'updated_at' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN updated_at TIMESTAMP')
            logger.info("Added updated_at column to pdf_documents")
            cursor.execute('UPDATE pdf_documents SET updated_at = created_at WHERE updated_at IS NULL')
        if 'published_year' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN published_year INTEGER')
            logger.info("Added published_year column to pdf_documents")
        if 'published_month_start' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN published_month_start INTEGER')
            logger.info("Added published_month_start column to pdf_documents")
        if 'published_month_end' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN published_month_end INTEGER')
            logger.info("Added published_month_end column to pdf_documents")
        if 'published_month_range_str' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN published_month_range_str TEXT')
            logger.info("Added published_month_range_str column to pdf_documents")

        # Enhanced PDF metadata fields
        if 'pdf_title' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN pdf_title TEXT')
            logger.info("Added pdf_title column to pdf_documents")
        if 'pdf_author' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN pdf_author TEXT')
            logger.info("Added pdf_author column to pdf_documents")
        if 'pdf_subject' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN pdf_subject TEXT')
            logger.info("Added pdf_subject column to pdf_documents")
        if 'pdf_keywords' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN pdf_keywords TEXT')
            logger.info("Added pdf_keywords column to pdf_documents")
        if 'pdf_creation_date' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN pdf_creation_date TIMESTAMP')
            logger.info("Added pdf_creation_date column to pdf_documents")
        if 'pdf_modification_date' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN pdf_modification_date TIMESTAMP')
            logger.info("Added pdf_modification_date column to pdf_documents")
        if 'pdf_version' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN pdf_version TEXT')
            logger.info("Added pdf_version column to pdf_documents")
        if 'pdf_producer' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN pdf_producer TEXT')
            logger.info("Added pdf_producer column to pdf_documents")
        if 'pdf_creator' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN pdf_creator TEXT')
            logger.info("Added pdf_creator column to pdf_documents")

        # Non-OCR PDF support columns
        if 'download_filename' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN download_filename TEXT')
            logger.info("Added download_filename column to pdf_documents")
        if 'has_non_ocr_version' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN has_non_ocr_version BOOLEAN DEFAULT FALSE')
            logger.info("Added has_non_ocr_version column to pdf_documents")
        if 'conversion_settings' not in columns:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN conversion_settings TEXT')
            logger.info("Added conversion_settings column to pdf_documents")

        # Create indexes for searchable metadata fields
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_pdf_documents_title ON pdf_documents(pdf_title)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_pdf_documents_author ON pdf_documents(pdf_author)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_pdf_documents_subject ON pdf_documents(pdf_subject)")

        # --- END AUTOMATIC COLUMN MIGRATION ---

        # Create url_content table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS url_content (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_url_id INTEGER NOT NULL,
                content_type TEXT NOT NULL CHECK(content_type IN ('text', 'image', 'link')),
                content TEXT NOT NULL,
                content_order INTEGER NOT NULL,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (source_url_id) REFERENCES source_urls(id) ON DELETE CASCADE
            )
        ''')

        # Create cover_images table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cover_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pdf_document_id INTEGER NOT NULL,
                image_path TEXT NOT NULL,
                image_url TEXT NOT NULL,
                source TEXT NOT NULL CHECK(source IN ('pdf_first_page', 'pdf_internal', 'url', 'default')),
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (pdf_document_id) REFERENCES pdf_documents(id) ON DELETE CASCADE
            )
        ''')

        # Create database_version table for schema migrations
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS database_version (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                version INTEGER NOT NULL,
                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                description TEXT
            )
        ''')

        # Create greeting_templates table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS greeting_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_type TEXT NOT NULL CHECK(template_type IN ('welcome', 'response', 'return_user', 'time_based')),
                greeting_text TEXT NOT NULL,
                context_conditions TEXT, -- JSON for conditions like time, role, etc.
                is_active BOOLEAN DEFAULT 1,
                weight INTEGER DEFAULT 1, -- For weighted random selection
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Create user_greeting_preferences table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_greeting_preferences (
                user_id INTEGER PRIMARY KEY,
                preferred_greeting_style TEXT DEFAULT 'friendly',
                last_greeting_used INTEGER,
                greeting_frequency TEXT DEFAULT 'every_response',
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
            )
        ''')

        # Create greeting_analytics table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS greeting_analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                client_name TEXT,
                greeting_template_id INTEGER,
                greeting_type TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_response_time REAL, -- Time to next user interaction
                FOREIGN KEY (greeting_template_id) REFERENCES greeting_templates(id)
            )
        ''')

        # Insert default time-based greeting templates if they don't exist
        cursor.execute("SELECT COUNT(*) FROM greeting_templates WHERE template_type = 'time_based'")
        time_based_count = cursor.fetchone()[0]

        if time_based_count == 0:
            # Morning greetings
            morning_greetings = [
                ("Good morning, {name}! Ready to explore some knowledge?", '{"time_of_day": "morning"}', 3),
                ("Morning, {name}! How can I help you today?", '{"time_of_day": "morning"}', 2),
                ("{time_greeting}, {name}! What would you like to learn about?", '{"time_of_day": "morning"}', 2),
                ("Good morning, {name}! I hope you're having a great start to your day.", '{"time_of_day": "morning"}', 1)
            ]

            # Afternoon greetings
            afternoon_greetings = [
                ("Good afternoon, {name}! What can I help you with?", '{"time_of_day": "afternoon"}', 3),
                ("Afternoon, {name}! How's your day going?", '{"time_of_day": "afternoon"}', 2),
                ("{time_greeting}, {name}! Ready to dive into some research?", '{"time_of_day": "afternoon"}', 2),
                ("Good afternoon, {name}! I hope you're having a productive day.", '{"time_of_day": "afternoon"}', 1)
            ]

            # Evening greetings
            evening_greetings = [
                ("Good evening, {name}! Working late?", '{"time_of_day": "evening"}', 3),
                ("Evening, {name}! How can I assist you tonight?", '{"time_of_day": "evening"}', 2),
                ("{time_greeting}, {name}! What brings you here this evening?", '{"time_of_day": "evening"}', 2),
                ("Good evening, {name}! I hope you're winding down well.", '{"time_of_day": "evening"}', 1)
            ]

            # Insert all time-based greetings
            all_greetings = morning_greetings + afternoon_greetings + evening_greetings

            for greeting_text, context_conditions, weight in all_greetings:
                cursor.execute('''
                    INSERT INTO greeting_templates
                    (template_type, greeting_text, context_conditions, weight, is_active)
                    VALUES (?, ?, ?, ?, ?)
                ''', ('time_based', greeting_text, context_conditions, weight, 1))

            logger.info(f"Inserted {len(all_greetings)} default time-based greeting templates")

        # Insert default return user greetings if they don't exist
        cursor.execute("SELECT COUNT(*) FROM greeting_templates WHERE template_type = 'return_user'")
        return_user_count = cursor.fetchone()[0]

        if return_user_count == 0:
            return_user_greetings = [
                ("Welcome back, {name}! Good to see you again.", '{"session_type": "returning"}', 3),
                ("Hello again, {name}! How can I help you today?", '{"session_type": "returning"}', 2),
                ("Nice to see you back, {name}! What would you like to explore?", '{"session_type": "returning"}', 2),
                ("{time_greeting}, {name}! Welcome back to the ERDB Knowledge Hub.", '{"session_type": "returning"}', 2)
            ]

            for greeting_text, context_conditions, weight in return_user_greetings:
                cursor.execute('''
                    INSERT INTO greeting_templates
                    (template_type, greeting_text, context_conditions, weight, is_active)
                    VALUES (?, ?, ?, ?, ?)
                ''', ('return_user', greeting_text, context_conditions, weight, 1))

            logger.info(f"Inserted {len(return_user_greetings)} default return user greeting templates")

        # Check current version
        cursor.execute("SELECT MAX(version) FROM database_version")
        result = cursor.fetchone()
        current_version = result[0] if result[0] is not None else 0

        # If this is a new database, set initial version
        if current_version == 0:
            cursor.execute('''
                INSERT INTO database_version (version, description)
                VALUES (1, 'Initial schema creation')
            ''')
            logger.info("Database initialized with version 1")

        # Create indexes for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_pdf_documents_filename ON pdf_documents(filename)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_pdf_documents_category ON pdf_documents(category)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_url_content_source_url_id ON url_content(source_url_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_url_content_content_type ON url_content(content_type)")
        cursor.execute("CREATE UNIQUE INDEX IF NOT EXISTS idx_cover_images_pdf_document_id ON cover_images(pdf_document_id)")

        # Create extracted_locations table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS extracted_locations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                location_text TEXT NOT NULL,
                location_type TEXT NOT NULL CHECK(location_type IN ('place_name', 'address', 'coordinates', 'landmark', 'region', 'municipality', 'city', 'barangay')),
                latitude REAL,
                longitude REAL,
                confidence_score REAL DEFAULT 0.0,
                context_snippet TEXT,
                geocoded_address TEXT,
                country TEXT,
                region TEXT,
                city TEXT,
                municipality TEXT,
                barangay TEXT,
                administrative_level TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Create location_sources table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS location_sources (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                location_id INTEGER NOT NULL,
                source_type TEXT NOT NULL CHECK(source_type IN ('pdf_document', 'chat_message', 'url_content')),
                source_id INTEGER NOT NULL,
                page_number INTEGER,
                extraction_method TEXT NOT NULL CHECK(extraction_method IN ('ner', 'regex', 'manual')),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (location_id) REFERENCES extracted_locations(id) ON DELETE CASCADE
            )
        ''')

        # Create geocoding_cache table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS geocoding_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                location_query TEXT UNIQUE NOT NULL,
                latitude REAL,
                longitude REAL,
                formatted_address TEXT,
                geocoding_service TEXT DEFAULT 'nominatim',
                confidence_score REAL DEFAULT 0.0,
                cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                status TEXT DEFAULT 'success' CHECK(status IN ('success', 'failed', 'partial')),
                country TEXT,
                region TEXT,
                city TEXT
            )
        ''')

        # Create indexes for greeting tables
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_greeting_templates_type ON greeting_templates(template_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_greeting_templates_active ON greeting_templates(is_active)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_greeting_analytics_session ON greeting_analytics(session_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_greeting_analytics_timestamp ON greeting_analytics(timestamp)")

        # Create indexes for location tables
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_extracted_locations_type ON extracted_locations(location_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_extracted_locations_coordinates ON extracted_locations(latitude, longitude)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_location_sources_type ON location_sources(source_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_location_sources_location_id ON location_sources(location_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_geocoding_cache_query ON geocoding_cache(location_query)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_geocoding_cache_expires ON geocoding_cache(expires_at)")

        # Run location extraction schema migration if needed
        migrate_location_extraction_schema(cursor)

        conn.commit()
        logger.info("Database schema initialized successfully")

        return True
    except sqlite3.Error as e:
        logger.error(f"Database initialization error: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()

def migrate_location_schema():
    """
    Migrate the location schema to support Philippine administrative divisions.
    This function adds new columns to existing tables if they don't exist and fixes CHECK constraints.
    """
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH, timeout=30.0)
        cursor = conn.cursor()

        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON")

        # Check if the extracted_locations table exists
        cursor.execute("""
            SELECT name FROM sqlite_master
            WHERE type='table' AND name='extracted_locations'
        """)

        if not cursor.fetchone():
            logger.info("extracted_locations table doesn't exist, skipping migration")
            return True

        # Check if the new columns exist in extracted_locations table
        cursor.execute("PRAGMA table_info(extracted_locations)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add municipality column if it doesn't exist
        if 'municipality' not in columns:
            cursor.execute("ALTER TABLE extracted_locations ADD COLUMN municipality TEXT")
            logger.info("Added municipality column to extracted_locations table")

        # Add barangay column if it doesn't exist
        if 'barangay' not in columns:
            cursor.execute("ALTER TABLE extracted_locations ADD COLUMN barangay TEXT")
            logger.info("Added barangay column to extracted_locations table")

        # Add administrative_level column if it doesn't exist
        if 'administrative_level' not in columns:
            cursor.execute("ALTER TABLE extracted_locations ADD COLUMN administrative_level TEXT")
            logger.info("Added administrative_level column to extracted_locations table")

        # Check if we need to update the CHECK constraint for location_type
        # SQLite doesn't support modifying CHECK constraints directly, so we need to recreate the table
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='extracted_locations'")
        table_sql = cursor.fetchone()[0]

        # Check if the constraint includes the new location types
        if ('municipality' not in table_sql or 'barangay' not in table_sql) and 'CHECK' in table_sql:
            logger.info("Updating CHECK constraint for location_type column")

            # Create a backup table
            cursor.execute("""
                CREATE TABLE extracted_locations_backup AS
                SELECT * FROM extracted_locations
            """)

            # Drop the original table
            cursor.execute("DROP TABLE extracted_locations")

            # Recreate the table with the updated CHECK constraint
            cursor.execute('''
                CREATE TABLE extracted_locations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    location_text TEXT NOT NULL,
                    location_type TEXT NOT NULL CHECK(location_type IN ('place_name', 'address', 'coordinates', 'landmark', 'region', 'municipality', 'city', 'barangay')),
                    latitude REAL,
                    longitude REAL,
                    confidence_score REAL DEFAULT 0.0,
                    context_snippet TEXT,
                    geocoded_address TEXT,
                    country TEXT,
                    region TEXT,
                    city TEXT,
                    municipality TEXT,
                    barangay TEXT,
                    administrative_level TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Restore data from backup
            cursor.execute("""
                INSERT INTO extracted_locations
                SELECT * FROM extracted_locations_backup
            """)

            # Drop the backup table
            cursor.execute("DROP TABLE extracted_locations_backup")

            # Recreate indexes
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_extracted_locations_type ON extracted_locations(location_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_extracted_locations_coordinates ON extracted_locations(latitude, longitude)")

            logger.info("Successfully updated CHECK constraint for location_type column")

        conn.commit()
        logger.info("Location schema migration completed successfully")
        return True

    except sqlite3.Error as e:
        logger.error(f"Error migrating location schema: {str(e)}")
        if conn:
            try:
                conn.rollback()
            except:
                pass
        return False
    except Exception as e:
        logger.error(f"Unexpected error during location schema migration: {str(e)}")
        return False
    finally:
        if conn:
            try:
                conn.close()
            except:
                pass

# Run this if the script is executed directly
if __name__ == "__main__":
    initialize_database()
    migrate_location_schema()
