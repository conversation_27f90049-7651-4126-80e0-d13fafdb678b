<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ page_title }} - ERDB Knowledge Hub</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .form-container {
            max-width: 600px;
            margin: 2rem auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .form-body {
            padding: 2rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }
        .required-field {
            color: #dc3545;
        }
        .back-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .back-link:hover {
            color: #5a6fd8;
            text-decoration: underline;
        }
        .csrf-error {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
        .checkbox-group {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        .checkbox-group .form-check {
            margin-bottom: 0.5rem;
        }
        .checkbox-group .form-check:last-child {
            margin-bottom: 0;
        }
        .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        .form-check-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h2><i class="fas fa-file-pdf me-2"></i>{{ page_title }}</h2>
                <p class="mb-0">To download the document <strong>{{ filename }}</strong>, please complete the form below.</p>
            </div>
            
            <div class="form-body">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form method="POST" action="{{ url_for('download_gated_pdf', filename=request.view_args.filename) }}" id="gated-form">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" id="csrf-token">
                    
                    {% for field in form.fields %}
                    <div class="mb-3">
                        <label for="{{ field.name }}" class="form-label">
                            {{ field.label }}
                            {% if field.required %}
                                <span class="required-field">*</span>
                            {% endif %}
                        </label>
                        {% if field.type == 'textarea' %}
                            <textarea class="form-control" 
                                      id="{{ field.name }}" 
                                      name="{{ field.name }}" 
                                      rows="4"
                                      {% if field.required %}required{% endif %} 
                                      placeholder="{{ field.placeholder }}"></textarea>
                        {% elif field.type == 'checkbox' %}
                            <div class="checkbox-group">
                                {% for option in field.options %}
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="{{ field.name }}_{{ loop.index }}" 
                                           name="{{ field.name }}" 
                                           value="{{ option }}"
                                           {% if field.required %}required{% endif %}>
                                    <label class="form-check-label" for="{{ field.name }}_{{ loop.index }}">
                                        {{ option }}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            {% if field.required %}
                            <div class="form-text text-muted">
                                <i class="fas fa-asterisk text-danger me-1"></i>Please select at least one option
                            </div>
                            {% endif %}
                        {% elif field.type == 'select' %}
                            <select class="form-select" 
                                    id="{{ field.name }}" 
                                    name="{{ field.name }}"
                                    {% if field.multiple %}multiple{% endif %}
                                    {% if field.required %}required{% endif %}>
                                <option value="">-- Select {{ field.label }} --</option>
                                {% for option in field.options %}
                                <option value="{{ option }}">{{ option }}</option>
                                {% endfor %}
                            </select>
                            {% if field.multiple %}
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>Hold Ctrl (or Cmd on Mac) to select multiple options
                            </div>
                            {% endif %}
                        {% else %}
                            <input type="{{ field.type }}" 
                                   class="form-control" 
                                   id="{{ field.name }}" 
                                   name="{{ field.name }}" 
                                   {% if field.required %}required{% endif %} 
                                   placeholder="{{ field.placeholder }}">
                        {% endif %}
                    </div>
                    {% endfor %}
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="submit-btn">
                            <i class="fas fa-download me-2"></i>Submit and Download
                        </button>
                    </div>
                </form>
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('main.index') }}" class="back-link">
                        <i class="fas fa-arrow-left me-1"></i>Back to Knowledge Hub
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // CSRF token validation and refresh
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('gated-form');
            const csrfTokenInput = document.getElementById('csrf-token');
            const submitBtn = document.getElementById('submit-btn');
            
            // Check if CSRF token is present
            if (!csrfTokenInput.value) {
                console.warn('CSRF token is missing, attempting to refresh...');
                refreshCSRFToken();
            }
            
            form.addEventListener('submit', function(e) {
                // Validate CSRF token before submission
                if (!csrfTokenInput.value) {
                    e.preventDefault();
                    alert('Security token is missing. Please refresh the page and try again.');
                    return false;
                }
                
                // Validate required checkbox fields
                const requiredCheckboxes = form.querySelectorAll('input[type="checkbox"][required]');
                for (let checkboxGroup of requiredCheckboxes) {
                    const fieldName = checkboxGroup.name;
                    const checkboxesInGroup = form.querySelectorAll(`input[name="${fieldName}"]:checked`);
                    if (checkboxesInGroup.length === 0) {
                        e.preventDefault();
                        alert(`Please select at least one option for "${checkboxGroup.closest('.mb-3').querySelector('label').textContent.replace('*', '').trim()}"`);
                        return false;
                    }
                }
                
                // Validate required select fields
                const requiredSelects = form.querySelectorAll('select[required]');
                for (let select of requiredSelects) {
                    if (select.multiple) {
                        const selectedOptions = Array.from(select.selectedOptions);
                        if (selectedOptions.length === 0) {
                            e.preventDefault();
                            alert(`Please select at least one option for "${select.closest('.mb-3').querySelector('label').textContent.replace('*', '').trim()}"`);
                            return false;
                        }
                    } else {
                        if (!select.value) {
                            e.preventDefault();
                            alert(`Please select an option for "${select.closest('.mb-3').querySelector('label').textContent.replace('*', '').trim()}"`);
                            return false;
                        }
                    }
                }
                
                // Disable submit button to prevent double submission
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                
                // Set a timer to close window after form submission (file download)
                setTimeout(function() {
                    // Show success message and close window
                    const successMsg = document.createElement('div');
                    successMsg.className = 'alert alert-success';
                    successMsg.innerHTML = '<i class="fas fa-check-circle me-2"></i>Download started successfully! Closing window...';
                    
                    // Insert success message at the top of form body
                    const formBody = document.querySelector('.form-body');
                    formBody.insertBefore(successMsg, formBody.firstChild);
                    
                    // Close window after 2 seconds
                    setTimeout(function() {
                        window.close();
                    }, 2000);
                }, 2000); // Wait 2 seconds for download to start
            });
        });
        
        async function refreshCSRFToken() {
            try {
                const response = await fetch('/api/csrf-token', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.csrf_token) {
                        // Update both meta tag and form input
                        const metaTag = document.querySelector('meta[name="csrf-token"]');
                        const csrfTokenInput = document.getElementById('csrf-token');
                        
                        if (metaTag) {
                            metaTag.setAttribute('content', data.csrf_token);
                        }
                        if (csrfTokenInput) {
                            csrfTokenInput.value = data.csrf_token;
                        }
                        
                        console.log('CSRF token refreshed successfully');
                    }
                }
            } catch (error) {
                console.error('Failed to refresh CSRF token:', error);
            }
        }
    </script>
</body>
</html> 