@echo off
REM === Start ScispaCy Microservice in a new window (Python 3.10) ===
REM start cmd /k "cd /d %~dp0 && call venv_310\Scripts\activate && python scripts/venv310_scispacy_microservice.py"

REM === Set environment variable for main app ===
set SCI_NAME_MICROSERVICE_URL=http://localhost:5005/detect_scientific_names

REM === Activate main app environment ===
call venv\Scripts\activate

REM === Start the main app ===
python -m app.__main__