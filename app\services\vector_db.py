import os
from langchain_chroma import Chroma
from langchain_ollama.embeddings import OllamaEmbeddings
import logging
import requests
from chromadb.config import Settings
import json
import hashlib

# Import ChromaDB performance monitoring
from app.utils.chroma_performance import (
    monitor_chroma_operation,
    monitor_similarity_search,
    monitor_add_documents,
    get_chroma_monitor
)
# Import caching system
from app.services.cache_service import cache_service, QueryCache

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Update to use unified database path
CHROMA_PATH = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
TEXT_EMBEDDING_MODEL = os.getenv("TEXT_EMBEDDING_MODEL", "mxbai-embed-large:latest")
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

# Fallback embedding models in order of preference
FALLBACK_EMBEDDING_MODELS = ["mxbai-embed-large:latest", "bge-m3:latest", "nomic-embed-text:latest"]

def check_embedding_model_availability(model_name):
    """Check if the specified embedding model is available in Ollama."""
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        if response.status_code != 200:
            logger.warning(f"Ollama service returned status code: {response.status_code}")
            return False

        # Check if the embedding model is available
        models = response.json().get("models", [])
        model_names = [model.get("name") for model in models]

        if model_name in model_names:
            logger.info(f"Embedding model '{model_name}' is available")
            return True
        else:
            logger.warning(f"Embedding model '{model_name}' not found in available models: {', '.join(model_names)}")
            return False
    except Exception as e:
        logger.warning(f"Failed to check embedding model availability: {str(e)}")
        return False

def _get_embedding_prompts():
    try:
        with open(os.path.join(os.path.dirname(__file__), '../../config/default_models.json'), 'r') as f:
            config = json.load(f)
        params = config.get('embedding_parameters', {})
        return params.get('embedding_prompt', ''), params.get('query_prompt', '')
    except Exception as e:
        logger.warning(f"Could not load embedding prompts from config: {e}")
        return '', ''

def generate_embedding_cache_key(model_name: str, text: str) -> str:
    """Generate a consistent cache key for embeddings"""
    key_data = f"embedding:{model_name}:{text}"
    return hashlib.md5(key_data.encode()).hexdigest()

def get_cached_embedding(model_name: str, text: str):
    """Get cached embedding if available"""
    cache_key = generate_embedding_cache_key(model_name, text)
    cached_embedding = cache_service.get(cache_key)
    if cached_embedding is not None:
        logger.debug(f"Cache hit for embedding: {text[:50]}...")
        return cached_embedding
    return None

def cache_embedding(model_name: str, text: str, embedding, ttl: int = 86400):
    """Cache embedding with 24-hour TTL by default"""
    cache_key = generate_embedding_cache_key(model_name, text)
    cache_service.set(cache_key, embedding, ttl)
    logger.debug(f"Cached embedding for: {text[:50]}...")

class CachedOllamaEmbeddings:
    """Wrapper for OllamaEmbeddings with caching support"""

    def __init__(self, model: str, base_url: str, **kwargs):
        self.model = model
        self.base_url = base_url
        self.ollama_embeddings = OllamaEmbeddings(
            model=model,
            base_url=base_url,
            **kwargs
        )

    def embed_query(self, text: str):
        """Embed query with caching"""
        # Check cache first
        cached_result = get_cached_embedding(self.model, text)
        if cached_result is not None:
            return cached_result

        # Generate embedding
        embedding = self.ollama_embeddings.embed_query(text)

        # Cache the result
        cache_embedding(self.model, text, embedding)

        return embedding

    def embed_documents(self, texts: list):
        """Embed documents with caching"""
        results = []
        cache_misses = []
        cache_miss_indices = []

        # Check cache for each document
        for i, text in enumerate(texts):
            cached_result = get_cached_embedding(self.model, text)
            if cached_result is not None:
                results.append(cached_result)
            else:
                results.append(None)  # Placeholder
                cache_misses.append(text)
                cache_miss_indices.append(i)

        # Generate embeddings for cache misses
        if cache_misses:
            logger.info(f"Generating embeddings for {len(cache_misses)}/{len(texts)} documents (cache misses)")
            new_embeddings = self.ollama_embeddings.embed_documents(cache_misses)

            # Cache new embeddings and update results
            for i, (text, embedding) in enumerate(zip(cache_misses, new_embeddings)):
                cache_embedding(self.model, text, embedding)
                results[cache_miss_indices[i]] = embedding
        else:
            logger.info(f"All {len(texts)} document embeddings found in cache")

        return results

# Remove PromptedOllamaEmbeddings and always use OllamaEmbeddings batching for all models

# Cache for Chroma instances
_chroma_cache = {}

def get_vector_db(category: str) -> Chroma:
    """
    Get or create a Chroma vector database for the specified category.
    Now uses unified database with metadata filtering.

    Args:
        category (str): The category name for the vector database.

    Returns:
        Chroma: The Chroma vector database instance.
    """
    try:
        # Return cached instance if available
        if category in _chroma_cache:
            logger.debug(f"Reusing cached Chroma DB for category: {category}")
            return _chroma_cache[category]

        # Use the unified vector database service instead of creating new instances
        from app.services.unified_vector_db import get_unified_vector_db
        unified_db = get_unified_vector_db()
        
        # Get the underlying Chroma instance from the unified database
        db = unified_db._get_db()
        
        # Cache the instance for this category
        _chroma_cache[category] = db
        logger.info(f"Using unified ChromaDB for category: {category}")
        return db

    except Exception as e:
        logger.error(f"Failed to get vector DB for category {category}: {str(e)}")
        raise

@monitor_similarity_search
def similarity_search_with_category_filter(query: str, category: str, k: int = 10, **kwargs):
    """
    Perform similarity search with category filtering.
    
    Args:
        query: Search query
        category: Category to filter by
        k: Number of results to return
        **kwargs: Additional search parameters
        
    Returns:
        List of similar documents
    """
    try:
        db = get_vector_db(category)
        
        # Use metadata filtering for category separation
        filter_dict = {"category": category}
        
        results = db.similarity_search(
            query,
            k=k,
            filter=filter_dict,
            **kwargs
        )
        
        logger.info(f"Found {len(results)} documents for query in category {category}")
        return results
        
    except Exception as e:
        logger.error(f"Failed to perform similarity search: {str(e)}")
        raise

@monitor_add_documents
def add_documents_with_category(documents, category: str, **kwargs):
    """
    Add documents to the unified database with category metadata.
    
    Args:
        documents: List of documents to add
        category: Category for the documents
        **kwargs: Additional metadata
    """
    try:
        db = get_vector_db(category)
        
        # Prepare documents with category metadata
        enhanced_documents = []
        for doc in documents:
            # Create a copy of the document with enhanced metadata
            enhanced_metadata = doc.metadata.copy() if hasattr(doc, 'metadata') else {}
            enhanced_metadata['category'] = category
            enhanced_metadata.update(kwargs)
            
            # Create new document with enhanced metadata
            from langchain.schema import Document
            enhanced_doc = Document(
                page_content=doc.page_content,
                metadata=enhanced_metadata
            )
            enhanced_documents.append(enhanced_doc)
        
        # Add documents using the standard add_documents method
        db.add_documents(enhanced_documents)
        logger.info(f"Added {len(documents)} documents to category: {category}")

        # Invalidate cached queries for this category since new documents were added
        try:
            invalidated_count = QueryCache.invalidate_category_cache(category)
            logger.info(f"Invalidated {invalidated_count} cached queries for category: {category}")
        except Exception as cache_error:
            logger.warning(f"Failed to invalidate cache for category {category}: {cache_error}")

    except Exception as e:
        logger.error(f"Failed to add documents: {str(e)}")
        raise