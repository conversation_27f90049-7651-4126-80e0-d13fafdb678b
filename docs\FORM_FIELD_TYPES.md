# Form Field Types Documentation

## Overview

The ERDB form system supports multiple field types for creating dynamic, user-friendly forms for PDF downloads. This document covers all supported field types, including the newly added checkbox and select field types.

## Supported Field Types

### 1. Text Input (`text`)
Standard single-line text input field.

**Properties:**
- `label` (required): Display label for the field
- `name` (required): Field name for form submission
- `placeholder` (optional): Placeholder text
- `required` (optional): Whether the field is required (default: false)
- `type`: Must be `"text"`

**Example:**
```json
{
    "label": "Full Name",
    "name": "full_name",
    "placeholder": "Enter your full name",
    "required": true,
    "type": "text"
}
```

### 2. Email Input (`email`)
Email input field with built-in email validation.

**Properties:**
- `label` (required): Display label for the field
- `name` (required): Field name for form submission
- `placeholder` (optional): Placeholder text
- `required` (optional): Whether the field is required (default: false)
- `type`: Must be `"email"`

**Example:**
```json
{
    "label": "Email Address",
    "name": "email",
    "placeholder": "Enter your email address",
    "required": true,
    "type": "email"
}
```

### 3. Telephone Input (`tel`)
Telephone number input field.

**Properties:**
- `label` (required): Display label for the field
- `name` (required): Field name for form submission
- `placeholder` (optional): Placeholder text
- `required` (optional): Whether the field is required (default: false)
- `type`: Must be `"tel"`

**Example:**
```json
{
    "label": "Phone Number",
    "name": "phone",
    "placeholder": "Enter your phone number",
    "required": false,
    "type": "tel"
}
```

### 4. Number Input (`number`)
Numeric input field.

**Properties:**
- `label` (required): Display label for the field
- `name` (required): Field name for form submission
- `placeholder` (optional): Placeholder text
- `required` (optional): Whether the field is required (default: false)
- `type`: Must be `"number"`

**Example:**
```json
{
    "label": "Age",
    "name": "age",
    "placeholder": "Enter your age",
    "required": false,
    "type": "number"
}
```

### 5. Text Area (`textarea`)
Multi-line text input field.

**Properties:**
- `label` (required): Display label for the field
- `name` (required): Field name for form submission
- `placeholder` (optional): Placeholder text
- `required` (optional): Whether the field is required (default: false)
- `type`: Must be `"textarea"`

**Example:**
```json
{
    "label": "Comments",
    "name": "comments",
    "placeholder": "Enter your comments",
    "required": false,
    "type": "textarea"
}
```

### 6. Checkbox Group (`checkbox`) - NEW
Multiple checkbox options allowing users to select one or more options.

**Properties:**
- `label` (required): Display label for the field
- `name` (required): Field name for form submission
- `options` (required): Array of option strings
- `required` (optional): Whether at least one option must be selected (default: false)
- `type`: Must be `"checkbox"`

**Example:**
```json
{
    "label": "Purpose of Download",
    "name": "purpose",
    "options": ["Research", "Education", "Policy", "Industry"],
    "required": true,
    "type": "checkbox"
}
```

**Features:**
- Multiple selections allowed
- Visual grouping with styled container
- Client-side validation for required fields
- Responsive design

### 7. Select Dropdown (`select`) - NEW
Dropdown select field with single or multiple selection options.

**Properties:**
- `label` (required): Display label for the field
- `name` (required): Field name for form submission
- `options` (required): Array of option strings
- `multiple` (optional): Whether multiple selections are allowed (default: false)
- `required` (optional): Whether an option must be selected (default: false)
- `type`: Must be `"select"`

**Single Select Example:**
```json
{
    "label": "Organization Type",
    "name": "org_type",
    "options": ["Academic", "Government", "Private", "Other"],
    "required": true,
    "type": "select"
}
```

**Multiple Select Example:**
```json
{
    "label": "Interests",
    "name": "interests",
    "options": ["Research", "Education", "Policy", "Industry"],
    "multiple": true,
    "required": true,
    "type": "select"
}
```

**Features:**
- Single or multiple selection modes
- Keyboard navigation support
- Client-side validation
- Visual indicators for multiple selection mode

## Form Submission Data Format

### Single Value Fields
Fields with single values (text, email, tel, number, textarea, single select) are submitted as strings:

```json
{
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "org_type": "Academic"
}
```

### Multiple Value Fields
Fields with multiple values (checkbox, multiple select) are submitted as arrays:

```json
{
    "purpose": ["Research", "Education"],
    "interests": ["Research", "Policy"]
}
```

## Validation Rules

### Required Field Validation
- **Single value fields**: Must have a non-empty value
- **Checkbox fields**: At least one option must be selected
- **Select fields**: At least one option must be selected (or multiple options for multiple select)

### Client-Side Validation
- Real-time validation feedback
- Form submission prevention for invalid data
- User-friendly error messages

### Server-Side Validation
- Data type validation
- Required field enforcement
- Data sanitization

## Styling and User Experience

### Checkbox Groups
- Styled container with light background
- Proper spacing between options
- Visual feedback on selection
- Accessible keyboard navigation

### Select Dropdowns
- Bootstrap-styled select elements
- Focus states with custom colors
- Multiple selection indicators
- Responsive design

### Form Layout
- Consistent spacing and typography
- Clear visual hierarchy
- Mobile-friendly design
- Accessibility compliance

## Implementation Details

### Template Rendering
The form system uses conditional rendering in templates:

```html
{% if field.type == 'checkbox' %}
    <!-- Checkbox group rendering -->
{% elif field.type == 'select' %}
    <!-- Select dropdown rendering -->
{% else %}
    <!-- Standard input rendering -->
{% endif %}
```

### JavaScript Validation
Client-side validation ensures:
- Required checkbox fields have at least one selection
- Required select fields have valid selections
- Form submission is prevented for invalid data

### Backend Processing
Server-side processing handles:
- Multiple value extraction for checkbox and select fields
- Data type conversion and validation
- Form submission storage

## Best Practices

### Field Design
1. **Clear Labels**: Use descriptive, user-friendly labels
2. **Logical Ordering**: Arrange fields in logical sequence
3. **Required Indicators**: Clearly mark required fields with asterisks
4. **Helpful Placeholders**: Provide useful placeholder text

### Checkbox Fields
1. **Option Clarity**: Use clear, specific option names
2. **Reasonable Count**: Limit to 5-7 options for usability
3. **Logical Grouping**: Group related options together
4. **Default Selection**: Consider pre-selecting common options

### Select Fields
1. **Option Organization**: Arrange options in logical order
2. **Clear Default**: Include a default "Please select" option
3. **Multiple Selection**: Use sparingly to avoid complexity
4. **Option Count**: Limit to 10-15 options for usability

### Form Structure
1. **Progressive Disclosure**: Start with essential fields
2. **Grouping**: Group related fields together
3. **Validation**: Provide immediate feedback
4. **Accessibility**: Ensure keyboard navigation and screen reader support

## Migration and Compatibility

### Backward Compatibility
- Existing forms continue to work unchanged
- New field types are optional additions
- No breaking changes to existing functionality

### Data Migration
- Existing form submissions remain valid
- New field types store data in compatible format
- Database schema supports all field types

## Troubleshooting

### Common Issues

1. **Checkbox Not Saving**
   - Ensure `options` array is defined
   - Check that field name is unique
   - Verify required validation logic

2. **Select Field Not Working**
   - Confirm `options` array is provided
   - Check `multiple` property setting
   - Verify JavaScript validation

3. **Form Submission Errors**
   - Check field name conflicts
   - Verify JSON format is valid
   - Ensure all required fields are present

### Debugging Tips

1. **Browser Console**: Check for JavaScript errors
2. **Network Tab**: Monitor form submission requests
3. **Form Preview**: Use admin preview to test forms
4. **Validation Logs**: Check server-side validation messages

## Future Enhancements

### Planned Features
1. **Conditional Fields**: Show/hide fields based on other field values
2. **Field Dependencies**: Require fields based on other selections
3. **Custom Validation**: User-defined validation rules
4. **Field Templates**: Pre-built field configurations

### Potential Improvements
1. **Rich Text Editor**: For textarea fields
2. **File Upload**: Support for file attachments
3. **Date/Time Picker**: Date and time input fields
4. **Rating System**: Star or numeric rating fields

## Support and Resources

### Documentation
- [Form Preview Feature](./FORM_PREVIEW_FEATURE.md)
- [Form Management Guide](./FORM_MANAGEMENT.md)
- [API Documentation](./API_DOCUMENTATION.md)

### Code Examples
- [Form Templates](../app/templates/)
- [Form Processing](../app/__main__.py)
- [Form Database](../app/utils/forms_db.py)

### Testing
- [Form Field Tests](../test_form_field_types.py)
- [Integration Tests](../tests/)
- [Manual Testing Guide](./TESTING_GUIDE.md)
