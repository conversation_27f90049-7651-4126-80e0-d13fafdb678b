"""
Unified Vector Database Service
Provides a single ChromaDB instance with metadata filtering for better performance.
"""

import os
import logging
import threading
from typing import List, Dict, Any, Optional
from langchain_chroma import Chroma
from langchain_ollama.embeddings import OllamaEmbeddings
from langchain.schema import Document
import chromadb
from chromadb.config import Settings

logger = logging.getLogger(__name__)

# Global lock for thread-safe singleton pattern
_unified_db_lock = threading.Lock()

class UnifiedVectorDB:
    """
    Unified vector database service that consolidates all collections into a single database
    with metadata filtering for category separation.
    """
    
    def __init__(self):
        self.persist_directory = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
        self.collection_name = "unified_collection"
        self.embedding_model = os.getenv("TEXT_EMBEDDING_MODEL", "mxbai-embed-large:latest")
        self._db = None
        self._embedding_function = None
        self._client = None
        
        # Ensure directory exists
        os.makedirs(self.persist_directory, exist_ok=True)
    
    def _get_embedding_function(self):
        """Get or create the embedding function."""
        if self._embedding_function is None:
            try:
                self._embedding_function = OllamaEmbeddings(model=self.embedding_model)
                logger.info(f"Initialized embedding function with model: {self.embedding_model}")
            except Exception as e:
                logger.error(f"Failed to initialize embedding function: {str(e)}")
                raise
        return self._embedding_function
    
    def _get_client(self):
        """Get or create the ChromaDB client."""
        if self._client is None:
            try:
                # Use consistent settings across all ChromaDB instances
                settings = Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
                self._client = chromadb.PersistentClient(
                    path=self.persist_directory,
                    settings=settings
                )
                logger.info(f"Initialized ChromaDB client at: {self.persist_directory}")
            except Exception as e:
                logger.error(f"Failed to initialize ChromaDB client: {str(e)}")
                raise
        return self._client
    
    def _get_db(self):
        """Get or create the ChromaDB instance with conflict resolution."""
        if self._db is None:
            try:
                embed_fn = self._get_embedding_function()
                
                # Try to create the ChromaDB instance with consistent settings
                try:
                    self._db = Chroma(
                        collection_name=self.collection_name,
                        persist_directory=self.persist_directory,
                        embedding_function=embed_fn
                    )
                    logger.info(f"Initialized unified ChromaDB at: {self.persist_directory}")
                except ValueError as ve:
                    # Handle ChromaDB instance conflict
                    error_msg = str(ve)
                    if "already exists" in error_msg and "different settings" in error_msg:
                        logger.warning(f"ChromaDB instance conflict detected: {error_msg}")
                        logger.info("Attempting to resolve conflict by reusing existing instance...")
                        
                        # Try to get the existing collection
                        try:
                            client = self._get_client()
                            collection = client.get_collection(name=self.collection_name)
                            
                            # Create a Chroma instance that wraps the existing collection
                            self._db = Chroma(
                                collection_name=self.collection_name,
                                persist_directory=self.persist_directory,
                                embedding_function=embed_fn,
                                client=client
                            )
                            logger.info("Successfully resolved ChromaDB instance conflict")
                        except Exception as resolve_error:
                            logger.error(f"Failed to resolve ChromaDB conflict: {str(resolve_error)}")
                            raise ValueError(f"ChromaDB instance conflict could not be resolved: {str(ve)}") from ve
                    else:
                        # Re-raise if it's not a conflict error
                        raise
                        
            except Exception as e:
                logger.error(f"Failed to initialize ChromaDB: {str(e)}")
                raise
        return self._db
    
    def _cleanup_instance(self):
        """Clean up the current instance."""
        try:
            if self._db is not None:
                # ChromaDB doesn't require explicit cleanup, but we can clear our reference
                self._db = None
            if self._client is not None:
                self._client = None
            if self._embedding_function is not None:
                self._embedding_function = None
            logger.debug("Cleaned up UnifiedVectorDB instance")
        except Exception as e:
            logger.warning(f"Error during instance cleanup: {str(e)}")
    
    def add_documents(self, documents: List[Document], category: str, **kwargs):
        """
        Add documents to the unified database with category metadata.
        
        Args:
            documents: List of documents to add
            category: Category for the documents
            **kwargs: Additional metadata
        """
        try:
            db = self._get_db()
            
            # Prepare metadata with category
            metadatas = []
            for doc in documents:
                metadata = doc.metadata.copy() if hasattr(doc, 'metadata') else {}
                metadata['category'] = category
                metadata.update(kwargs)
                metadatas.append(metadata)
            
            # Add documents
            db.add_documents(documents, metadatas=metadatas)
            logger.info(f"Added {len(documents)} documents to category: {category}")
            
        except Exception as e:
            logger.error(f"Failed to add documents: {str(e)}")
            raise
    
    def similarity_search(self, query: str, category: Optional[str] = None, 
                         k: int = 10, **kwargs) -> List[Document]:
        """
        Perform similarity search with optional category filtering.
        
        Args:
            query: Search query
            category: Optional category filter
            k: Number of results to return
            **kwargs: Additional search parameters
            
        Returns:
            List of similar documents
        """
        try:
            db = self._get_db()
            
            # Prepare filter
            filter_dict = {}
            if category:
                filter_dict['category'] = category
            
            # Perform search
            results = db.similarity_search(
                query, 
                k=k,
                filter=filter_dict if filter_dict else None,
                **kwargs
            )
            
            logger.info(f"Found {len(results)} documents for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Failed to perform similarity search: {str(e)}")
            raise
    
    def similarity_search_with_score(self, query: str, category: Optional[str] = None,
                                   k: int = 10, **kwargs) -> List[tuple]:
        """
        Perform similarity search with scores.
        
        Args:
            query: Search query
            category: Optional category filter
            k: Number of results to return
            **kwargs: Additional search parameters
            
        Returns:
            List of (document, score) tuples
        """
        try:
            db = self._get_db()
            
            # Prepare filter
            filter_dict = {}
            if category:
                filter_dict['category'] = category
            
            # Perform search
            results = db.similarity_search_with_score(
                query,
                k=k,
                filter=filter_dict if filter_dict else None,
                **kwargs
            )
            
            logger.info(f"Found {len(results)} documents with scores for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Failed to perform similarity search with scores: {str(e)}")
            raise
    
    def test_connection(self) -> bool:
        """
        Test the ChromaDB connection and return True if successful.
        
        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            db = self._get_db()
            # Try to access the collection to test the connection
            collection = db._collection
            if collection is not None:
                logger.info("ChromaDB connection test successful")
                return True
            else:
                logger.error("ChromaDB connection test failed: collection is None")
                return False
        except Exception as e:
            logger.error(f"ChromaDB connection test failed: {str(e)}")
            return False
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the collection.
        
        Returns:
            Dict containing collection statistics
        """
        try:
            db = self._get_db()
            collection = db._collection
            
            if collection is None:
                return {"error": "Collection not available"}
            
            # Get basic stats
            stats = {
                "collection_name": self.collection_name,
                "persist_directory": self.persist_directory,
                "embedding_model": self.embedding_model
            }
            
            try:
                # Get document count
                count = collection.count()
                stats["document_count"] = count
            except Exception as e:
                stats["document_count"] = f"Error: {str(e)}"
            
            try:
                # Get collection metadata
                metadata = collection.metadata
                stats["metadata"] = metadata
            except Exception as e:
                stats["metadata"] = f"Error: {str(e)}"
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {str(e)}")
            return {"error": str(e)}
    
    def delete_documents(self, category: Optional[str] = None, 
                        filter_dict: Optional[Dict] = None):
        """
        Delete documents from the collection.
        
        Args:
            category: Optional category filter
            filter_dict: Additional filter criteria
        """
        try:
            db = self._get_db()
            
            # Prepare filter
            if filter_dict is None:
                filter_dict = {}
            if category:
                filter_dict['category'] = category
            
            # Delete documents
            if filter_dict:
                db.delete(filter=filter_dict)
                logger.info(f"Deleted documents with filter: {filter_dict}")
            else:
                logger.warning("No filter provided for deletion. Use with caution.")
                
        except Exception as e:
            logger.error(f"Failed to delete documents: {str(e)}")
            raise
    
    def update_document_metadata(self, document_id: str, metadata: Dict[str, Any]):
        """
        Update metadata for a specific document.
        
        Args:
            document_id: ID of the document to update
            metadata: New metadata
        """
        try:
            # Use chromadb directly for metadata updates
            client = chromadb.PersistentClient(path=self.persist_directory)
            collection = client.get_collection(name=self.collection_name)
            
            # Update metadata
            collection.update(ids=[document_id], metadatas=[metadata])
            logger.info(f"Updated metadata for document: {document_id}")
            
        except Exception as e:
            logger.error(f"Failed to update document metadata: {str(e)}")
            raise
    
    def get_document_by_id(self, document_id: str) -> Optional[Document]:
        """
        Get a specific document by ID.
        
        Args:
            document_id: ID of the document
            
        Returns:
            Document if found, None otherwise
        """
        try:
            # Use chromadb directly
            client = chromadb.PersistentClient(path=self.persist_directory)
            collection = client.get_collection(name=self.collection_name)
            
            # Get document
            results = collection.get(ids=[document_id])
            
            if results['ids']:
                # Convert to Document format
                doc = Document(
                    page_content=results['documents'][0],
                    metadata=results['metadatas'][0] or {}
                )
                return doc
            else:
                return None
                
        except Exception as e:
            logger.error(f"Failed to get document by ID: {str(e)}")
            return None
    
    def optimize_collection(self):
        """
        Optimize the collection for better performance.
        """
        try:
            # Use chromadb directly for optimization
            client = chromadb.PersistentClient(path=self.persist_directory)
            collection = client.get_collection(name=self.collection_name)
            
            # ChromaDB automatically optimizes, but we can trigger some operations
            logger.info("Collection optimization completed")
            
        except Exception as e:
            logger.error(f"Failed to optimize collection: {str(e)}")
            raise

# Global instance for easy access
_unified_db = None

def get_unified_vector_db() -> UnifiedVectorDB:
    """
    Get the global unified vector database instance with thread-safe singleton pattern.
    
    Returns:
        UnifiedVectorDB instance
    """
    global _unified_db, _unified_db_lock
    
    # Double-checked locking pattern for thread safety
    if _unified_db is None:
        with _unified_db_lock:
            if _unified_db is None:
                try:
                    _unified_db = UnifiedVectorDB()
                    logger.info("Created new UnifiedVectorDB instance")
                except Exception as e:
                    logger.error(f"Failed to create UnifiedVectorDB instance: {str(e)}")
                    raise
    return _unified_db

def reset_unified_vector_db():
    """
    Reset the global unified vector database instance.
    This is useful for testing or when conflicts occur.
    """
    global _unified_db, _unified_db_lock
    
    with _unified_db_lock:
        if _unified_db is not None:
            _unified_db._cleanup_instance()
            _unified_db = None
            logger.info("Reset UnifiedVectorDB instance")

def get_vector_db_by_category(category: str) -> UnifiedVectorDB:
    """
    Get vector database instance for a specific category.
    This maintains compatibility with existing code while using the unified database.
    
    Args:
        category: Category name (used for metadata filtering)
        
    Returns:
        UnifiedVectorDB instance
    """
    # Return the unified instance - category filtering is handled in search methods
    return get_unified_vector_db() 