#!/usr/bin/env python3
"""
Test script to verify checkbox and select field types work correctly.
"""

import json
import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_form_field_types():
    """Test the new checkbox and select field types."""
    print("=== Testing Form Field Types ===")
    
    # Test form with new field types
    test_form = {
        "name": "Test Form",
        "description": "Test form with checkbox and select fields",
        "fields": [
            {
                "label": "Full Name",
                "name": "full_name",
                "placeholder": "Enter your full name",
                "required": True,
                "type": "text"
            },
            {
                "label": "Email Address",
                "name": "email",
                "placeholder": "Enter your email address",
                "required": True,
                "type": "email"
            },
            {
                "label": "Organization",
                "name": "organization",
                "placeholder": "Enter your organization (optional)",
                "required": False,
                "type": "text"
            },
            {
                "label": "Purpose of Download",
                "name": "purpose",
                "options": ["Research", "Education", "Policy", "Industry"],
                "required": True,
                "type": "checkbox"
            },
            {
                "label": "Organization Type",
                "name": "org_type",
                "options": ["Academic", "Government", "Private", "Other"],
                "required": True,
                "type": "select"
            },
            {
                "label": "Interests",
                "name": "interests",
                "options": ["Research", "Education", "Policy", "Industry"],
                "multiple": True,
                "required": True,
                "type": "select"
            }
        ]
    }
    
    print("✅ Test form created successfully")
    print(f"📊 Form has {len(test_form['fields'])} fields")
    
    # Test field type counting
    field_types = {}
    for field in test_form['fields']:
        field_type = field['type']
        if field_type not in field_types:
            field_types[field_type] = 0
        field_types[field_type] += 1
    
    print("\n📈 Field type distribution:")
    for field_type, count in field_types.items():
        print(f"  - {field_type}: {count}")
    
    # Test checkbox field validation
    checkbox_fields = [f for f in test_form['fields'] if f['type'] == 'checkbox']
    print(f"\n✅ Found {len(checkbox_fields)} checkbox fields")
    
    for field in checkbox_fields:
        if 'options' not in field:
            print(f"❌ Checkbox field '{field['name']}' missing options")
        else:
            print(f"✅ Checkbox field '{field['name']}' has {len(field['options'])} options")
    
    # Test select field validation
    select_fields = [f for f in test_form['fields'] if f['type'] == 'select']
    print(f"\n✅ Found {len(select_fields)} select fields")
    
    for field in select_fields:
        if 'options' not in field:
            print(f"❌ Select field '{field['name']}' missing options")
        else:
            multiple = field.get('multiple', False)
            print(f"✅ Select field '{field['name']}' has {len(field['options'])} options (multiple: {multiple})")
    
    # Test JSON serialization
    try:
        json_str = json.dumps(test_form, indent=2)
        print(f"\n✅ Form serializes to JSON successfully ({len(json_str)} characters)")
    except Exception as e:
        print(f"❌ Failed to serialize form to JSON: {e}")
        return False
    
    # Test JSON deserialization
    try:
        parsed_form = json.loads(json_str)
        print("✅ Form deserializes from JSON successfully")
    except Exception as e:
        print(f"❌ Failed to deserialize form from JSON: {e}")
        return False
    
    print("\n🎉 All tests passed!")
    return True

def test_form_submission_data():
    """Test form submission data processing."""
    print("\n=== Testing Form Submission Data Processing ===")
    
    # Simulate form submission data
    form_data = {
        'full_name': 'John Doe',
        'email': '<EMAIL>',
        'organization': 'Test Org',
        'purpose': ['Research', 'Education'],  # Multiple checkbox selections
        'org_type': 'Academic',  # Single select
        'interests': ['Research', 'Policy']  # Multiple select
    }
    
    print("✅ Form submission data created")
    print(f"📊 Submission has {len(form_data)} fields")
    
    # Test checkbox data processing
    checkbox_data = form_data.get('purpose', [])
    if isinstance(checkbox_data, list):
        print(f"✅ Checkbox data processed correctly: {checkbox_data}")
    else:
        print(f"❌ Checkbox data not processed as list: {checkbox_data}")
    
    # Test select data processing
    select_data = form_data.get('org_type', '')
    if isinstance(select_data, str):
        print(f"✅ Single select data processed correctly: {select_data}")
    else:
        print(f"❌ Single select data not processed as string: {select_data}")
    
    # Test multiple select data processing
    multiple_select_data = form_data.get('interests', [])
    if isinstance(multiple_select_data, list):
        print(f"✅ Multiple select data processed correctly: {multiple_select_data}")
    else:
        print(f"❌ Multiple select data not processed as list: {multiple_select_data}")
    
    print("\n🎉 Form submission data processing tests passed!")
    return True

if __name__ == "__main__":
    print("🚀 Starting form field type tests...\n")
    
    success = True
    
    # Run tests
    if not test_form_field_types():
        success = False
    
    if not test_form_submission_data():
        success = False
    
    print(f"\n{'🎉 All tests passed!' if success else '❌ Some tests failed!'}")
    sys.exit(0 if success else 1)
