"""
Enhanced Chunking Service using LlamaIndex with Ollama Embeddings
Provides semantic-aware and adaptive text chunking capabilities with content-aware approaches.
"""

import os
import logging
import re
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from dataclasses import dataclass

# Set up logger early
logger = logging.getLogger(__name__)

# LangChain imports for compatibility
from langchain.schema import Document

# LlamaIndex imports with correct paths
try:
    # Correct import paths for LlamaIndex 0.13.0+
    from llama_index.core.text_splitter import SentenceSplitter
    from llama_index.core.node_parser.text.semantic_splitter import SemanticSplitterNodeParser
    from llama_index.embeddings.ollama import OllamaEmbedding
    LLAMAINDEX_AVAILABLE = True
    logger.info("✅ LlamaIndex successfully imported")
except ImportError as e:
    logger.warning(f"LlamaIndex import failed: {e}")
    try:
        # Try alternative import paths for older versions
        from llama_index.text_splitter import SentenceSplitter
        from llama_index.embeddings.ollama import OllamaEmbedding
        # For older versions, SemanticSplitter might not be available
        SemanticSplitterNodeParser = None
        LLAMAINDEX_AVAILABLE = True
        logger.info("✅ LlamaIndex imported with legacy paths")
    except ImportError as e2:
        logger.warning(f"LlamaIndex legacy import also failed: {e2}")
        # Fallback to LangChain
        from langchain.text_splitter import RecursiveCharacterTextSplitter
        LLAMAINDEX_AVAILABLE = False
        SemanticSplitterNodeParser = None

from config.chunking_config import get_chunking_config
from app.services.content_type_detector import ContentTypeDetector
from app.services.intelligent_content_extractor import IntelligentContentExtractor, TextBlock
from app.utils.performance_monitor import performance_monitor

@dataclass
class ChunkingStrategy:
    """Represents a chunking strategy with parameters"""
    name: str
    chunk_size: int
    chunk_overlap: int
    method: str
    content_aware: bool
    confidence: float
    min_chunk_size: int = 100
    metadata: Dict[str, Any] = None

@dataclass
class ContentBoundary:
    """Represents a content boundary for chunking"""
    position: int
    boundary_type: str
    confidence: float
    metadata: Dict[str, Any] = None

class ChunkingError(Exception):
    """Custom exception for chunking operations"""
    pass

class EnhancedChunkingService:
    """Enhanced chunking service with semantic awareness and adaptive strategies"""
    
    def __init__(self, config=None):
        self.config = config or get_chunking_config()
        self.content_detector = ContentTypeDetector()
        self.content_extractor = IntelligentContentExtractor()
        self._embed_model = None
        self._fallback_splitter = None
        
        # Content-aware chunking parameters
        self.content_aware_params = {
            'semantic_threshold': 0.7,
            'hierarchical_weight': 0.8,
            'boundary_confidence_threshold': 0.6,
            'max_chunk_size': 2000,
            'min_chunk_size': 100,
            'adaptive_overlap_ratio': 0.2
        }
        
        # Initialize fallback splitter
        self._init_fallback_splitter()
        
        # Try to initialize LlamaIndex components
        if LLAMAINDEX_AVAILABLE:
            self._init_llamaindex_components()
        else:
            logger.warning("LlamaIndex not available. Using LangChain fallback.")
    
    def _init_fallback_splitter(self):
        """Initialize fallback RecursiveCharacterTextSplitter"""
        try:
            from langchain.text_splitter import RecursiveCharacterTextSplitter
            self._fallback_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.config.default_chunk_size,
                chunk_overlap=self.config.default_chunk_overlap
            )
            logger.info("Initialized fallback text splitter")
        except Exception as e:
            logger.error(f"Failed to initialize fallback splitter: {e}")
            raise ChunkingError(f"Cannot initialize any text splitter: {e}")
    
    def _init_llamaindex_components(self):
        """Initialize LlamaIndex embedding model"""
        try:
            # Try LlamaIndex Ollama embedding first
            self._embed_model = OllamaEmbedding(
                model_name=self.config.embedding_model,
                base_url=self.config.ollama_base_url,
                request_timeout=60.0
            )
            logger.info(f"Initialized LlamaIndex Ollama embedding: {self.config.embedding_model}")
        except Exception as e:
            logger.warning(f"Failed to initialize LlamaIndex Ollama embedding: {e}")
            # Try to create a compatibility wrapper using existing Ollama setup
            try:
                self._embed_model = self._create_ollama_compatibility_wrapper()
                logger.info("Created Ollama compatibility wrapper for semantic chunking")
            except Exception as wrapper_error:
                logger.warning(f"Failed to create compatibility wrapper: {wrapper_error}")
                self._embed_model = None
    
    def _create_ollama_compatibility_wrapper(self):
        """Create a compatibility wrapper for Ollama embeddings"""
        try:
            from app.services.ollama_service import OllamaService
            ollama_service = OllamaService()
            
            class OllamaCompatibilityWrapper:
                def __init__(self, langchain_embeddings):
                    self.embeddings = langchain_embeddings
                
                def get_text_embedding(self, text):
                    return self.embeddings.embed_query(text)
                
                def get_text_embeddings(self, texts):
                    return self.embeddings.embed_documents(texts)
            
            return OllamaCompatibilityWrapper(ollama_service)
        except Exception as e:
            logger.error(f"Failed to create Ollama compatibility wrapper: {e}")
            return None
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def content_aware_chunk(self, text: str, content_type: str = None, 
                           hierarchical_structure: Dict = None) -> List[Document]:
        """
        Content-aware chunking with hierarchical structure preservation.
        
        Args:
            text: Text to chunk
            content_type: Type of content (academic, technical, general, etc.)
            hierarchical_structure: Hierarchical structure information from content extractor
            
        Returns:
            List of chunked documents with metadata
        """
        try:
            logger.info(f"Starting content-aware chunking for {len(text)} characters")
            
            # 1. Analyze content structure
            content_analysis = self._analyze_content_structure(text, content_type, hierarchical_structure)
            
            # 2. Detect content boundaries
            boundaries = self._detect_content_boundaries(text, content_analysis)
            
            # 3. Select optimal chunking strategy
            strategy = self._select_content_aware_strategy(content_analysis, boundaries)
            
            # 4. Apply hierarchical chunking
            chunks = self._apply_hierarchical_chunking(text, boundaries, strategy)
            
            # 5. Post-process chunks
            processed_chunks = self._post_process_chunks(chunks, content_analysis)
            
            logger.info(f"Content-aware chunking completed. Generated {len(processed_chunks)} chunks")
            return processed_chunks
            
        except Exception as e:
            logger.error(f"Content-aware chunking failed: {str(e)}")
            return self._fallback_content_aware_chunk(text, content_type)
    
    def _analyze_content_structure(self, text: str, content_type: str = None, 
                                 hierarchical_structure: Dict = None) -> Dict[str, Any]:
        """Analyze the structure of the content"""
        analysis = {
            'content_type': content_type or self.content_detector.detect_content_type(text),
            'length': len(text),
            'complexity': self._calculate_content_complexity(text),
            'structure_elements': self._detect_structure_elements(text),
            'semantic_density': self._calculate_semantic_density(text),
            'hierarchical_info': hierarchical_structure or {}
        }
        
        # Add content-specific analysis
        if analysis['content_type'] == 'academic':
            analysis.update(self._analyze_academic_structure(text))
        elif analysis['content_type'] == 'technical':
            analysis.update(self._analyze_technical_structure(text))
        elif analysis['content_type'] == 'newsletter':
            analysis.update(self._analyze_newsletter_structure(text))
        
        return analysis
    
    def _calculate_content_complexity(self, text: str) -> float:
        """Calculate content complexity score"""
        # Factor 1: Sentence complexity
        sentences = re.split(r'[.!?]+', text)
        avg_sentence_length = np.mean([len(s.split()) for s in sentences if s.strip()])
        
        # Factor 2: Vocabulary complexity
        words = text.lower().split()
        unique_words = set(words)
        vocabulary_richness = len(unique_words) / len(words) if words else 0
        
        # Factor 3: Technical terms
        technical_patterns = [
            r'\b[A-Z]{2,}\b',  # Acronyms
            r'\b\d+\.\d+\b',   # Numbers with decimals
            r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b'  # Title case phrases
        ]
        technical_count = sum(len(re.findall(pattern, text)) for pattern in technical_patterns)
        technical_density = technical_count / len(words) if words else 0
        
        # Normalize and combine factors
        complexity = (
            min(avg_sentence_length / 20, 1.0) * 0.4 +
            vocabulary_richness * 0.3 +
            min(technical_density * 10, 1.0) * 0.3
        )
        
        return min(complexity, 1.0)
    
    def _detect_structure_elements(self, text: str) -> Dict[str, Any]:
        """Detect structural elements in the text"""
        elements = {
            'headers': [],
            'sections': [],
            'paragraphs': [],
            'lists': [],
            'tables': [],
            'figures': []
        }
        
        # Detect headers
        header_patterns = [
            r'^[A-Z][A-Z\s\d]+$',  # ALL CAPS headers
            r'^\d+\.\s+[A-Z][a-z\s]+$',  # Numbered sections
            r'^[A-Z][a-z\s]+:$',  # Title case with colon
        ]
        
        lines = text.split('\n')
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # Check for headers
            for pattern in header_patterns:
                if re.match(pattern, line):
                    elements['headers'].append({
                        'text': line,
                        'position': i,
                        'level': self._determine_header_level(line)
                    })
                    break
            
            # Check for lists
            if re.match(r'^[\-\*•]\s+', line) or re.match(r'^\d+\.\s+', line):
                elements['lists'].append({
                    'text': line,
                    'position': i
                })
            
            # Check for potential table content
            if '\t' in line or '|' in line:
                elements['tables'].append({
                    'text': line,
                    'position': i
                })
        
        return elements
    
    def _determine_header_level(self, header_text: str) -> int:
        """Determine the level of a header"""
        if re.match(r'^[A-Z][A-Z\s\d]+$', header_text):
            return 1  # Main header
        elif re.match(r'^\d+\.\s+', header_text):
            return 2  # Section header
        elif re.match(r'^[A-Z][a-z\s]+:$', header_text):
            return 3  # Subsection header
        else:
            return 4  # Minor header
    
    def _calculate_semantic_density(self, text: str) -> float:
        """Calculate semantic density of the text"""
        # Count meaningful words (exclude common stop words)
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        words = text.lower().split()
        meaningful_words = [w for w in words if w not in stop_words and len(w) > 2]
        
        semantic_density = len(meaningful_words) / len(words) if words else 0
        return min(semantic_density, 1.0)
    
    def _analyze_academic_structure(self, text: str) -> Dict[str, Any]:
        """Analyze academic paper structure"""
        structure = {
            'has_abstract': False,
            'has_introduction': False,
            'has_methodology': False,
            'has_results': False,
            'has_conclusion': False,
            'has_references': False,
            'citation_count': 0
        }
        
        # Check for academic sections
        section_patterns = {
            'abstract': r'\b(?:Abstract|ABSTRACT)\b',
            'introduction': r'\b(?:Introduction|INTRODUCTION)\b',
            'methodology': r'\b(?:Methodology|Methods|METHODOLOGY|METHODS)\b',
            'results': r'\b(?:Results|RESULTS)\b',
            'conclusion': r'\b(?:Conclusion|CONCLUSION)\b',
            'references': r'\b(?:References|REFERENCES|Bibliography|BIBLIOGRAPHY)\b'
        }
        
        for section, pattern in section_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                structure[f'has_{section}'] = True
        
        # Count citations
        citation_patterns = [
            r'\[[\d\w\s]+\]',  # [1], [2], etc.
            r'\([A-Za-z\s]+\d{4}\)',  # (Author 2023)
            r'\b\d{4}\b'  # Year references
        ]
        
        for pattern in citation_patterns:
            structure['citation_count'] += len(re.findall(pattern, text))
        
        return structure
    
    def _analyze_technical_structure(self, text: str) -> Dict[str, Any]:
        """Analyze technical document structure"""
        structure = {
            'has_code_blocks': False,
            'has_tables': False,
            'has_diagrams': False,
            'has_procedures': False,
            'technical_terms': []
        }
        
        # Check for code blocks
        if re.search(r'```[\s\S]*?```', text) or re.search(r'`[^`]+`', text):
            structure['has_code_blocks'] = True
        
        # Check for tables
        if re.search(r'\|.*\|', text) or re.search(r'\t', text):
            structure['has_tables'] = True
        
        # Check for procedures
        if re.search(r'\b(?:Step|Procedure|Process)\s+\d+', text, re.IGNORECASE):
            structure['has_procedures'] = True
        
        # Extract technical terms
        technical_patterns = [
            r'\b[A-Z]{2,}\b',  # Acronyms
            r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b'  # Title case terms
        ]
        
        for pattern in technical_patterns:
            terms = re.findall(pattern, text)
            structure['technical_terms'].extend(terms)
        
        return structure
    
    def _analyze_newsletter_structure(self, text: str) -> Dict[str, Any]:
        """Analyze newsletter structure"""
        structure = {
            'has_articles': False,
            'has_sidebars': False,
            'has_images': False,
            'article_count': 0
        }
        
        # Check for article patterns
        article_patterns = [
            r'\b(?:Article|Story|Feature)\b',
            r'^\d+\.\s+[A-Z]',  # Numbered articles
        ]
        
        for pattern in article_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                structure['has_articles'] = True
                structure['article_count'] += len(re.findall(pattern, text, re.IGNORECASE))
        
        return structure
    
    def _detect_content_boundaries(self, text: str, content_analysis: Dict) -> List[ContentBoundary]:
        """Detect content boundaries for chunking"""
        boundaries = []
        
        # 1. Structural boundaries (headers, sections)
        structural_boundaries = self._detect_structural_boundaries(text, content_analysis)
        boundaries.extend(structural_boundaries)
        
        # 2. Semantic boundaries (topic changes)
        semantic_boundaries = self._detect_semantic_boundaries(text, content_analysis)
        boundaries.extend(semantic_boundaries)
        
        # 3. Natural boundaries (paragraphs, sentences)
        natural_boundaries = self._detect_natural_boundaries(text)
        boundaries.extend(natural_boundaries)
        
        # Sort boundaries by position
        boundaries.sort(key=lambda b: b.position)
        
        # Remove duplicate boundaries
        unique_boundaries = self._deduplicate_boundaries(boundaries)
        
        return unique_boundaries
    
    def _detect_structural_boundaries(self, text: str, content_analysis: Dict) -> List[ContentBoundary]:
        """Detect structural boundaries based on headers and sections"""
        boundaries = []
        
        structure_elements = content_analysis.get('structure_elements', {})
        
        for header in structure_elements.get('headers', []):
            position = text.find(header['text'])
            if position >= 0:
                boundary = ContentBoundary(
                    position=position,
                    boundary_type='structural',
                    confidence=0.9,
                    metadata={
                        'header_level': header['level'],
                        'header_text': header['text']
                    }
                )
                boundaries.append(boundary)
        
        return boundaries
    
    def _detect_semantic_boundaries(self, text: str, content_analysis: Dict) -> List[ContentBoundary]:
        """Detect semantic boundaries based on topic changes"""
        boundaries = []
        
        # Split into sentences
        sentences = re.split(r'[.!?]+', text)
        current_position = 0
        
        for i, sentence in enumerate(sentences):
            if not sentence.strip():
                current_position += len(sentence) + 1
                continue
            
            # Check for topic change indicators
            topic_change_indicators = [
                r'\b(?:However|Nevertheless|On the other hand|In contrast)\b',
                r'\b(?:Furthermore|Moreover|Additionally|Also)\b',
                r'\b(?:In conclusion|Finally|To summarize)\b',
                r'\b(?:First|Second|Third|Finally)\b'
            ]
            
            for indicator in topic_change_indicators:
                if re.search(indicator, sentence, re.IGNORECASE):
                    boundary = ContentBoundary(
                        position=current_position,
                        boundary_type='semantic',
                        confidence=0.7,
                        metadata={
                            'indicator': indicator,
                            'sentence': sentence.strip()
                        }
                    )
                    boundaries.append(boundary)
                    break
            
            current_position += len(sentence) + 1
        
        return boundaries
    
    def _detect_natural_boundaries(self, text: str) -> List[ContentBoundary]:
        """Detect natural boundaries like paragraphs and sentences"""
        boundaries = []
        
        # Paragraph boundaries
        paragraphs = text.split('\n\n')
        current_position = 0
        
        for paragraph in paragraphs:
            if paragraph.strip():
                boundary = ContentBoundary(
                    position=current_position,
                    boundary_type='paragraph',
                    confidence=0.5,
                    metadata={'paragraph_length': len(paragraph)}
                )
                boundaries.append(boundary)
            
            current_position += len(paragraph) + 2  # +2 for '\n\n'
        
        return boundaries
    
    def _deduplicate_boundaries(self, boundaries: List[ContentBoundary]) -> List[ContentBoundary]:
        """Remove duplicate boundaries that are too close to each other"""
        if not boundaries:
            return []
        
        unique_boundaries = [boundaries[0]]
        
        for boundary in boundaries[1:]:
            # Check if this boundary is too close to the last one
            last_boundary = unique_boundaries[-1]
            distance = boundary.position - last_boundary.position
            
            if distance > 50:  # Minimum distance between boundaries
                unique_boundaries.append(boundary)
            else:
                # Keep the boundary with higher confidence
                if boundary.confidence > last_boundary.confidence:
                    unique_boundaries[-1] = boundary
        
        return unique_boundaries
    
    def _select_content_aware_strategy(self, content_analysis: Dict, 
                                     boundaries: List[ContentBoundary]) -> ChunkingStrategy:
        """Select the optimal chunking strategy based on content analysis"""
        content_type = content_analysis['content_type']
        complexity = content_analysis['complexity']
        length = content_analysis['length']
        
        # Base strategy parameters
        if content_type == 'academic':
            base_chunk_size = 800
            base_overlap = 200
            method = 'hierarchical_semantic'
        elif content_type == 'technical':
            base_chunk_size = 600
            base_overlap = 150
            method = 'structural_semantic'
        elif content_type == 'newsletter':
            base_chunk_size = 1000
            base_overlap = 250
            method = 'article_based'
        else:
            base_chunk_size = 800
            base_overlap = 200
            method = 'adaptive_semantic'
        
        # Adjust based on complexity
        if complexity > 0.7:
            base_chunk_size = int(base_chunk_size * 0.8)
            base_overlap = int(base_overlap * 1.2)
        elif complexity < 0.3:
            base_chunk_size = int(base_chunk_size * 1.2)
            base_overlap = int(base_overlap * 0.8)
        
        # Adjust based on length
        if length > 10000:
            base_chunk_size = int(base_chunk_size * 1.1)
        elif length < 1000:
            base_chunk_size = int(base_chunk_size * 0.9)
        
        # Calculate confidence based on boundary quality
        boundary_confidence = np.mean([b.confidence for b in boundaries]) if boundaries else 0.5
        
        strategy = ChunkingStrategy(
            name=f"{content_type}_content_aware",
            chunk_size=base_chunk_size,
            chunk_overlap=base_overlap,
            method=method,
            content_aware=True,
            confidence=boundary_confidence,
            metadata={
                'content_type': content_type,
                'complexity': complexity,
                'boundary_count': len(boundaries)
            }
        )
        
        return strategy
    
    def _apply_hierarchical_chunking(self, text: str, boundaries: List[ContentBoundary], 
                                   strategy: ChunkingStrategy) -> List[Document]:
        """Apply hierarchical chunking based on content boundaries"""
        chunks = []
        
        if not boundaries:
            # Fallback to standard chunking
            return self._standard_chunking(text, strategy)
        
        # Create chunks based on boundaries
        start_pos = 0
        
        for i, boundary in enumerate(boundaries):
            end_pos = boundary.position
            
            # Extract chunk text
            chunk_text = text[start_pos:end_pos].strip()
            
            if len(chunk_text) >= strategy.min_chunk_size:
                # Create document with metadata
                metadata = {
                    'chunk_type': boundary.boundary_type,
                    'boundary_confidence': boundary.confidence,
                    'chunk_strategy': strategy.name,
                    'content_aware': True,
                    'start_position': start_pos,
                    'end_position': end_pos
                }
                
                if boundary.metadata:
                    metadata.update(boundary.metadata)
                
                chunk = Document(
                    page_content=chunk_text,
                    metadata=metadata
                )
                chunks.append(chunk)
            
            start_pos = end_pos
        
        # Handle the last chunk
        final_chunk_text = text[start_pos:].strip()
        if len(final_chunk_text) >= strategy.min_chunk_size:
            metadata = {
                'chunk_type': 'final',
                'chunk_strategy': strategy.name,
                'content_aware': True,
                'start_position': start_pos,
                'end_position': len(text)
            }
            
            chunk = Document(
                page_content=final_chunk_text,
                metadata=metadata
            )
            chunks.append(chunk)
        
        return chunks
    
    def _standard_chunking(self, text: str, strategy: ChunkingStrategy) -> List[Document]:
        """Standard chunking when no boundaries are detected"""
        try:
            if LLAMAINDEX_AVAILABLE and self._embed_model:
                # Use LlamaIndex semantic splitter
                splitter = self.get_semantic_splitter(
                    buffer_size=strategy.chunk_size,
                    breakpoint_percentile_threshold=95
                )
                chunks = splitter.split_text(text)
            else:
                # Use fallback splitter
                chunks = self._fallback_splitter.split_text(text)
            
            # Convert to Document objects
            documents = []
            for i, chunk in enumerate(chunks):
                metadata = {
                    'chunk_type': 'standard',
                    'chunk_strategy': strategy.name,
                    'content_aware': False,
                    'chunk_index': i
                }
                
                document = Document(
                    page_content=chunk,
                    metadata=metadata
                )
                documents.append(document)
            
            return documents
            
        except Exception as e:
            logger.error(f"Standard chunking failed: {str(e)}")
            return self._fallback_chunk([Document(page_content=text)], {})
    
    def _post_process_chunks(self, chunks: List[Document], content_analysis: Dict) -> List[Document]:
        """Post-process chunks to ensure quality and consistency"""
        processed_chunks = []
        
        for chunk in chunks:
            # Clean up chunk text
            cleaned_text = self._clean_chunk_text(chunk.page_content)
            
            if len(cleaned_text) >= self.content_aware_params['min_chunk_size']:
                # Update metadata
                chunk.metadata.update({
                    'cleaned_length': len(cleaned_text),
                    'content_type': content_analysis['content_type'],
                    'complexity': content_analysis['complexity']
                })
                
                # Update page content
                chunk.page_content = cleaned_text
                processed_chunks.append(chunk)
        
        return processed_chunks
    
    def _clean_chunk_text(self, text: str) -> str:
        """Clean up chunk text"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        # Ensure proper sentence endings
        if text and not text.endswith(('.', '!', '?')):
            # Find the last sentence boundary
            last_sentence = re.split(r'[.!?]', text)[-1]
            if len(last_sentence.strip()) < 50:  # Short incomplete sentence
                text = text.rsplit('.', 1)[0] + '.' if '.' in text else text
        
        return text
    
    def _fallback_content_aware_chunk(self, text: str, content_type: str) -> List[Document]:
        """Fallback chunking when content-aware chunking fails"""
        try:
            logger.warning("Using fallback content-aware chunking")
            
            # Use simple sentence-based chunking
            if self._fallback_splitter:
                chunks = self._fallback_splitter.split_text(text)
                return [Document(page_content=chunk, metadata={'chunk_type': 'fallback'}) for chunk in chunks]
            else:
                # Simple character-based chunking
                chunk_size = 1000
                overlap = 200
                chunks = []
                
                for i in range(0, len(text), chunk_size - overlap):
                    chunk_text = text[i:i + chunk_size]
                    if chunk_text.strip():
                        chunks.append(Document(
                            page_content=chunk_text.strip(),
                            metadata={'chunk_type': 'fallback', 'chunk_index': len(chunks)}
                        ))
                
                return chunks
                
        except Exception as e:
            logger.error(f"Fallback content-aware chunking failed: {str(e)}")
            return [Document(page_content=text[:1000], metadata={'chunk_type': 'error_fallback'})]

    def _fallback_chunk(self, documents: List[Document], metadata: Dict = None) -> List[Document]:
        """Fallback chunking method for when other methods fail"""
        try:
            logger.warning("Using fallback chunking")
            
            if not documents:
                return []
            
            # Simple chunking of the first document
            text = documents[0].page_content if documents else ""
            if not text:
                return []
            
            # Use simple character-based chunking
            chunk_size = 1000
            overlap = 200
            chunks = []
            
            for i in range(0, len(text), chunk_size - overlap):
                chunk_text = text[i:i + chunk_size]
                if chunk_text.strip():
                    chunk_metadata = metadata.copy() if metadata else {}
                    chunk_metadata.update({
                        'chunk_type': 'fallback',
                        'chunk_index': len(chunks),
                        'original_length': len(text)
                    })
                    chunks.append(Document(
                        page_content=chunk_text.strip(),
                        metadata=chunk_metadata
                    ))
            
            return chunks
            
        except Exception as e:
            logger.error(f"Fallback chunking failed: {str(e)}")
            return [Document(page_content="Error in chunking", metadata={'chunk_type': 'error'})]

    def get_semantic_splitter(self, chunk_size: int = 1000, chunk_overlap: int = 200) -> Any:
        """Get a semantic text splitter for chunking"""
        try:
            if LLAMAINDEX_AVAILABLE and SemanticSplitterNodeParser:
                # Use LlamaIndex semantic splitter
                return SemanticSplitterNodeParser(
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap
                )
            elif self._fallback_splitter:
                # Use fallback splitter
                return self._fallback_splitter
            else:
                # Create a simple sentence splitter
                from langchain.text_splitter import RecursiveCharacterTextSplitter
                return RecursiveCharacterTextSplitter(
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap,
                    separators=["\n\n", "\n", ". ", " ", ""]
                )
        except Exception as e:
            logger.error(f"Error creating semantic splitter: {str(e)}")
            # Return a simple character splitter as last resort
            from langchain.text_splitter import RecursiveCharacterTextSplitter
            return RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                separators=["\n\n", "\n", ". ", " ", ""]
            )

    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def adaptive_chunk(self, documents: List[Document], content_type: str = None) -> List[Document]:
        """
        Adaptive chunking method that automatically selects the best chunking strategy
        based on content analysis and document characteristics.
        
        Args:
            documents: List of documents to chunk
            content_type: Optional content type (technical, scientific, narrative, general)
            
        Returns:
            List of chunked documents with enhanced metadata
        """
        try:
            if not documents:
                logger.warning("No documents provided for adaptive chunking")
                return []
            
            logger.info(f"Starting adaptive chunking for {len(documents)} documents")
            
            all_chunks = []
            
            for i, doc in enumerate(documents):
                try:
                    # Extract text content
                    text = doc.page_content
                    if not text or not text.strip():
                        logger.warning(f"Document {i} has no content, skipping")
                        continue
                    
                    # Detect content type if not provided
                    if not content_type:
                        content_type = self.content_detector.detect_content_type(text)
                        logger.debug(f"Detected content type for document {i}: {content_type}")
                    
                    # Apply content-aware chunking
                    chunks = self.content_aware_chunk(text, content_type)
                    
                    # Enhance metadata for each chunk
                    for chunk in chunks:
                        # Preserve original document metadata
                        chunk.metadata.update(doc.metadata)
                        chunk.metadata.update({
                            'chunking_method': 'adaptive',
                            'content_type': content_type,
                            'original_document_index': i,
                            'chunking_service': 'EnhancedChunkingService'
                        })
                    
                    all_chunks.extend(chunks)
                    logger.debug(f"Document {i}: Created {len(chunks)} chunks")
                    
                except Exception as e:
                    logger.error(f"Error chunking document {i}: {str(e)}")
                    # Fallback chunking for this document
                    try:
                        fallback_chunks = self._fallback_chunk([doc], {'chunking_method': 'fallback'})
                        all_chunks.extend(fallback_chunks)
                        logger.info(f"Applied fallback chunking to document {i}")
                    except Exception as fallback_error:
                        logger.error(f"Fallback chunking also failed for document {i}: {str(fallback_error)}")
                        # Add the original document as a single chunk
                        all_chunks.append(doc)
            
            logger.info(f"Adaptive chunking completed. Created {len(all_chunks)} total chunks from {len(documents)} documents")
            return all_chunks
            
        except Exception as e:
            logger.error(f"Adaptive chunking failed: {str(e)}")
            # Return fallback chunking for all documents
            return self._fallback_chunk(documents, {'chunking_method': 'error_fallback'})

    def parallel_chunk_processing(self, documents: List[Document], max_workers: int = None) -> List[Document]:
        """
        Process documents in parallel for improved performance.
        
        Args:
            documents: List of documents to process
            max_workers: Maximum number of worker threads (default: min(32, len(documents)))
            
        Returns:
            List of chunked documents
        """
        if not documents:
            return []
        
        if max_workers is None:
            max_workers = min(32, len(documents))
        
        logger.info(f"Starting parallel chunk processing with {max_workers} workers")
        
        all_chunks = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit chunking tasks
            future_to_doc = {
                executor.submit(self.adaptive_chunk, [doc]): doc 
                for doc in documents
            }
            
            # Collect results
            for future in as_completed(future_to_doc):
                try:
                    chunks = future.result()
                    all_chunks.extend(chunks)
                except Exception as e:
                    doc = future_to_doc[future]
                    logger.error(f"Error in parallel chunking: {str(e)}")
                    # Add original document as fallback
                    all_chunks.append(doc)
        
        logger.info(f"Parallel chunk processing completed. Created {len(all_chunks)} chunks")
        return all_chunks
